import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/context/ThemeContext';

const ThemeToggle = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="relative h-12 w-12 rounded-2xl bg-primary-foreground/10 hover:bg-accent/20 transition-all duration-300 hover:scale-110 group glow-accent"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      <div className="relative w-6 h-6">
        {/* Sun icon - visible in dark mode */}
        <Sun
          className={`absolute inset-0 h-6 w-6 text-primary-foreground transition-all duration-500 ${
            theme === 'dark'
              ? 'rotate-0 scale-100 opacity-100'
              : 'rotate-90 scale-0 opacity-0'
          }`}
        />
        {/* Moon icon - visible in light mode */}
        <Moon
          className={`absolute inset-0 h-6 w-6 text-primary-foreground transition-all duration-500 ${
            theme === 'light'
              ? 'rotate-0 scale-100 opacity-100'
              : '-rotate-90 scale-0 opacity-0'
          }`}
        />
      </div>

      {/* Modern glow effect on hover */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-accent/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 glow-accent" />
    </Button>
  );
};

export default ThemeToggle;
