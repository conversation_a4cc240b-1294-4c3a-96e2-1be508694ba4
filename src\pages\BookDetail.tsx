
import React from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, ShoppingCart, Truck, Shield, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useBooks } from '@/context/BookContext';
import { useCart } from '@/context/CartContext';
import { toast } from '@/hooks/use-toast';
import RecommendedBooks from '@/components/ui/recommended-books';

const BookDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { getBookById } = useBooks();
  const { addToCart } = useCart();
  
  const book = id ? getBookById(id) : null;

  if (!book) {
    return (
      <div className="container mx-auto px-4 py-8 bg-background text-foreground">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-classic-black dark:text-foreground mb-4">Book not found</h1>
          <Link to="/books">
            <Button className="bg-elegant-gold dark:bg-accent hover:bg-elegant-gold/90 dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground">
              Browse All Books
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    addToCart(book);
    toast({
      title: "Added to Cart",
      description: `${book.title} has been added to your cart.`,
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 bg-background text-foreground transition-colors duration-300">
      {/* Back Button */}
      <Link to="/books" className="inline-flex items-center text-deep-red dark:text-primary hover:text-deep-red/80 dark:hover:text-primary/80 mb-6 transition-colors duration-300">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to All Books
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Book Image */}
        <div className="space-y-4">
          <div className="aspect-[3/4] bg-gray-100 dark:bg-muted rounded-lg overflow-hidden border dark:border-border shadow-lg dark:shadow-xl">
            <img
              src={book.image}
              alt={book.title}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Book Details */}
        <div className="space-y-6">
          <div>
            <h1 className="text-4xl font-bold text-classic-black dark:text-foreground mb-2">{book.title}</h1>
            <p className="text-xl text-gray-600 dark:text-muted-foreground mb-4">by {book.author}</p>
            <p className="text-3xl font-bold text-deep-red dark:text-primary">${book.price.toFixed(2)}</p>
          </div>

          {/* Stock Status */}
          <div>
            {book.stock > 5 && (
              <p className="text-green-600 dark:text-green-400 font-medium">✓ In Stock</p>
            )}
            {book.stock <= 5 && book.stock > 0 && (
              <p className="text-orange-500 dark:text-orange-400 font-medium">⚠ Only {book.stock} left in stock</p>
            )}
            {book.stock === 0 && (
              <p className="text-red-500 dark:text-red-400 font-medium">✗ Out of Stock</p>
            )}
          </div>

          {/* Add to Cart Button */}
          <Button
            onClick={handleAddToCart}
            disabled={book.stock === 0}
            size="lg"
            className="w-full bg-elegant-gold dark:bg-accent hover:bg-elegant-gold/90 dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground font-bold py-4 text-lg"
          >
            <ShoppingCart className="h-5 w-5 mr-2" />
            {book.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
          </Button>

          {/* Book Description */}
          <Card className="bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-classic-black dark:text-card-foreground mb-3">Description</h3>
              <p className="text-gray-700 dark:text-muted-foreground leading-relaxed">{book.description}</p>
            </CardContent>
          </Card>

          {/* Book Details */}
          <Card className="bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-classic-black dark:text-card-foreground mb-3">Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-muted-foreground">Category:</span>
                  <span className="font-medium text-foreground dark:text-card-foreground">{book.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-muted-foreground">Author:</span>
                  <span className="font-medium text-foreground dark:text-card-foreground">{book.author}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-muted-foreground">Price:</span>
                  <span className="font-bold text-deep-red dark:text-primary">${book.price.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large hover:shadow-medium dark:hover:shadow-xl transition-all duration-300">
              <CardContent className="p-4 text-center">
                <Truck className="h-8 w-8 text-elegant-gold dark:text-accent mx-auto mb-2" />
                <p className="text-sm font-medium text-foreground dark:text-card-foreground">Fast Delivery</p>
              </CardContent>
            </Card>
            <Card className="bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large hover:shadow-medium dark:hover:shadow-xl transition-all duration-300">
              <CardContent className="p-4 text-center">
                <Shield className="h-8 w-8 text-elegant-gold dark:text-accent mx-auto mb-2" />
                <p className="text-sm font-medium text-foreground dark:text-card-foreground">Payment on Delivery</p>
              </CardContent>
            </Card>
            <Card className="bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large hover:shadow-medium dark:hover:shadow-xl transition-all duration-300">
              <CardContent className="p-4 text-center">
                <MessageCircle className="h-8 w-8 text-elegant-gold dark:text-accent mx-auto mb-2" />
                <p className="text-sm font-medium text-foreground dark:text-card-foreground">WhatsApp Support</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Recommended Books */}
      <div className="mt-16">
        <RecommendedBooks currentBookId={book.id} />
      </div>
    </div>
  );
};

export default BookDetail;
