
export interface Book {
  id: string;
  title: string;
  author: string;
  price: number;
  description: string;
  image: string;
  category: string;
  stock: number;
  featured?: boolean;
}

export interface CartItem {
  book: Book;
  quantity: number;
}

export interface Order {
  id: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  items: CartItem[];
  total: number;
  status: 'Pending Confirmation' | 'Confirmed' | 'Delivered' | 'Cancelled';
  createdAt: string;
}

export interface Admin {
  username: string;
  password: string;
}
