
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import BookCard from '@/components/ui/book-card';
import { useBooks } from '@/context/BookContext';
import { Star, TrendingUp } from 'lucide-react';

interface RecommendedBooksProps {
  currentBookId?: string;
  title?: string;
}

const RecommendedBooks: React.FC<RecommendedBooksProps> = ({ 
  currentBookId, 
  title = "You Might Also Like" 
}) => {
  const { books } = useBooks();
  
  // Get random books excluding current one
  const recommendedBooks = books
    .filter(book => book.id !== currentBookId)
    .sort(() => Math.random() - 0.5)
    .slice(0, 3);

  if (recommendedBooks.length === 0) return null;

  return (
    <section className="py-12">
      <div className="flex items-center space-x-3 mb-8">
        <TrendingUp className="h-6 w-6 text-elegant-gold" />
        <h3 className="text-2xl font-bold text-classic-black">{title}</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {recommendedBooks.map(book => (
          <BookCard key={book.id} book={book} />
        ))}
      </div>
    </section>
  );
};

export default RecommendedBooks;
