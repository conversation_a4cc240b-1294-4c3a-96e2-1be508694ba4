
import React, { useState, useEffect, useRef } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { useBooks } from '@/context/BookContext';
import { Link } from 'react-router-dom';

const SearchBar = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const { books } = useBooks();
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (searchTerm.trim()) {
      const filtered = books.filter(book =>
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase())
      ).slice(0, 5);
      setResults(filtered);
      setIsOpen(true);
    } else {
      setResults([]);
      setIsOpen(false);
    }
  }, [searchTerm, books]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const clearSearch = () => {
    setSearchTerm('');
    setIsOpen(false);
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-md">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-clean-white/70 dark:text-clean-white/70" />
        <Input
          type="text"
          placeholder="Search books, authors..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 pr-10 bg-clean-white/10 dark:bg-clean-white/10 border-clean-white/20 dark:border-clean-white/20 text-clean-white dark:text-clean-white placeholder:text-clean-white/70 dark:placeholder:text-clean-white/70 focus:bg-clean-white dark:focus:bg-clean-white focus:text-classic-black dark:focus:text-classic-black focus:placeholder:text-gray-500 dark:focus:placeholder:text-gray-500"
        />
        {searchTerm && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-clean-white/70 dark:text-clean-white/70 hover:text-clean-white dark:hover:text-clean-white"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {isOpen && results.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 bg-card dark:bg-popover shadow-lg dark:shadow-xl border dark:border-border">
          <CardContent className="p-2">
            {results.map(book => (
              <Link
                key={book.id}
                to={`/book/${book.id}`}
                onClick={() => setIsOpen(false)}
                className="flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-accent rounded transition-colors"
              >
                <img src={book.image} alt={book.title} className="w-8 h-10 object-cover rounded border dark:border-border" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-classic-black dark:text-popover-foreground truncate">{book.title}</p>
                  <p className="text-xs text-gray-600 dark:text-muted-foreground truncate">{book.author}</p>
                </div>
                <span className="text-sm font-bold text-deep-red dark:text-primary">${book.price}</span>
              </Link>
            ))}
            {results.length === 5 && (
              <Link
                to={`/books?search=${searchTerm}`}
                onClick={() => setIsOpen(false)}
                className="block p-2 text-center text-sm text-deep-red dark:text-primary hover:bg-gray-50 dark:hover:bg-accent rounded"
              >
                View all results
              </Link>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SearchBar;
