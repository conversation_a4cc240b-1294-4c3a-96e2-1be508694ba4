@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Modern Light Mode - Sophisticated & Premium Design System */
    --background: 220 14% 96%; /* Sophisticated Light Grey #F8F9FB */
    --foreground: 224 71% 4%; /* Rich Charcoal #0F172A */

    --card: 0 0% 100%; /* Pure White Cards */
    --card-foreground: 224 71% 4%; /* Rich Charcoal */

    --popover: 0 0% 100%; /* Pure White */
    --popover-foreground: 224 71% 4%; /* Rich Charcoal */

    --primary: 346 77% 49%; /* Modern Crimson #E11D48 */
    --primary-foreground: 0 0% 100%; /* Pure White */

    --secondary: 220 14% 93%; /* Light Secondary #EBEEF3 */
    --secondary-foreground: 224 71% 4%; /* Rich Charcoal */

    --muted: 220 14% 93%; /* Subtle Background #EBEEF3 */
    --muted-foreground: 220 9% 46%; /* Muted Text #6B7280 */

    --accent: 43 74% 66%; /* Warm Amber #F59E0B */
    --accent-foreground: 224 71% 4%; /* Rich Charcoal */

    --success: 142 76% 36%; /* Fresh Green #059669 */
    --success-foreground: 0 0% 100%; /* Pure White */

    --warning: 38 92% 50%; /* Vibrant Orange #F97316 */
    --warning-foreground: 0 0% 100%; /* Pure White */

    --destructive: 0 84% 60%; /* Modern Red #EF4444 */
    --destructive-foreground: 0 0% 100%; /* Pure White */

    --border: 220 13% 91%; /* Soft Border #E2E8F0 */
    --input: 220 13% 91%; /* Input Background #E2E8F0 */
    --ring: 346 77% 49%; /* Primary Focus Ring */

    --radius: 0.75rem; /* Increased border radius for modern look */

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Modern Dark Mode - Sophisticated & Premium Design System */
    --background: 224 71% 4%; /* Rich Dark Navy #0F172A */
    --foreground: 210 40% 98%; /* Crisp White #F8FAFC */

    --card: 220 13% 9%; /* Elevated Dark Surface #1E293B */
    --card-foreground: 210 40% 98%; /* Crisp White #F8FAFC */

    --popover: 220 13% 9%; /* Elevated Dark Surface #1E293B */
    --popover-foreground: 210 40% 98%; /* Crisp White #F8FAFC */

    --primary: 346 77% 49%; /* Consistent Modern Crimson #E11D48 */
    --primary-foreground: 210 40% 98%; /* Crisp White #F8FAFC */

    --secondary: 215 28% 17%; /* Dark Secondary #334155 */
    --secondary-foreground: 210 40% 98%; /* Crisp White #F8FAFC */

    --muted: 215 28% 17%; /* Subtle Dark Background #334155 */
    --muted-foreground: 215 20% 65%; /* Muted Text #94A3B8 */

    --accent: 43 96% 56%; /* Vibrant Gold #F59E0B */
    --accent-foreground: 224 71% 4%; /* Rich Dark Navy */

    --success: 142 76% 36%; /* Fresh Green #059669 */
    --success-foreground: 210 40% 98%; /* Crisp White */

    --warning: 38 92% 50%; /* Vibrant Orange #F97316 */
    --warning-foreground: 210 40% 98%; /* Crisp White */

    --destructive: 0 84% 60%; /* Modern Red #EF4444 */
    --destructive-foreground: 210 40% 98%; /* Crisp White */

    --border: 215 28% 17%; /* Subtle Dark Border #334155 */
    --input: 220 13% 9%; /* Input Background #1E293B */
    --ring: 43 96% 56%; /* Vibrant Gold Focus Ring */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-body antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }

  /* Smooth theme transitions for all elements */
  * {
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  }

  /* Prevent flash of unstyled content during theme switch */
  html {
    color-scheme: light dark;
  }

  /* Enhanced focus styles for dark mode */
  .dark *:focus-visible {
    outline: 2px solid hsl(var(--accent));
    outline-offset: 2px;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Better text rendering */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
    text-wrap: balance;
  }

  p {
    text-wrap: pretty;
  }
}

@layer components {
  /* Modern Component Design System */
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply bg-accent hover:bg-accent/90 text-accent-foreground font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105 active:scale-95;
  }

  .card-modern {
    @apply bg-card border border-border rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:scale-[1.02];
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:scale-[1.02];
  }

  .text-gradient-modern {
    @apply bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent;
  }

  .section-padding {
    @apply py-20 lg:py-32;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Modern Input Styles */
  .input-modern {
    @apply bg-input border border-border rounded-xl px-4 py-3 text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300;
  }

  /* Modern Badge Styles */
  .badge-modern {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent/10 text-accent border border-accent/20;
  }

  .badge-success {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-700 border border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-700;
  }

  .badge-warning {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-700 border border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700;
  }

  /* Enhanced Light mode specific styles */
  .light-gradient-bg {
    background: linear-gradient(135deg, #FAFBFC 0%, #F8FAFC 100%);
  }

  .light-card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .light-card-shadow-hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .light-border-soft {
    border-color: #E2E8F0;
  }

  /* Enhanced Dark mode specific styles */
  .dark .hero-bg {
    @apply bg-deep-red-800;
  }

  .dark .card-bg {
    @apply bg-card border-border;
  }

  .dark .text-muted {
    @apply text-muted-foreground;
  }

  /* Dark mode button styles */
  .dark .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground;
  }

  .dark .btn-secondary {
    background-color: #F0C419; /* Illuminated Gold */
    color: #121212; /* Charcoal Grey */
  }

  .dark .btn-secondary:hover {
    background-color: #E6B800; /* Slightly darker gold */
  }

  /* Dark mode card enhancements */
  .dark .card-hover {
    @apply hover:bg-card/80;
  }

  /* Enhanced dark mode gradients */
  .dark .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%);
  }

  .dark .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)/0.8) 100%);
  }

  .dark .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)/0.95) 100%);
  }

  /* Enhanced dark mode shadows */
  .dark .shadow-glow {
    box-shadow: 0 0 20px hsl(var(--accent)/0.3);
  }

  .dark .shadow-primary-glow {
    box-shadow: 0 0 20px hsl(var(--primary)/0.3);
  }

  /* Dark mode border enhancements */
  .dark .border-glow {
    border: 1px solid hsl(var(--accent)/0.5);
    box-shadow: 0 0 10px hsl(var(--accent)/0.2);
  }

  /* Dark mode text enhancements */
  .dark .text-glow {
    text-shadow: 0 0 10px hsl(var(--accent)/0.5);
  }

  /* Dark mode text gradients */
  .dark .text-gradient {
    background: linear-gradient(to right, #9B1C22, #F0C419);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Dark mode shadows */
  .dark .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-large {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 20px 25px -5px rgba(0, 0, 0, 0.3);
  }
}

@layer utilities {
  /* Modern Utility Classes */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Modern Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 50%, hsl(var(--accent)) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)/0.8) 100%);
  }

  .gradient-surface {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)/0.95) 100%);
  }

  /* Modern Shadow System */
  .shadow-soft {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .shadow-medium {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-large {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Modern Glow Effects */
  .glow-primary {
    box-shadow: 0 0 20px hsl(var(--primary)/0.3);
  }

  .glow-accent {
    box-shadow: 0 0 20px hsl(var(--accent)/0.3);
  }

  .glow-success {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  /* Modern Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px hsl(var(--accent)/0.3); }
    50% { box-shadow: 0 0 30px hsl(var(--accent)/0.5); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes bounce-in {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes slide-up {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, hsl(var(--accent)/0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.6s ease-out;
  }

  /* Hover Animations */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .hover-glow:hover {
    animation: pulse-glow 1s ease-in-out;
  }

  /* Hero section animations */
  .hero-float {
    animation: float 6s ease-in-out infinite;
  }

  .hero-glow {
    animation: glow 4s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }

  /* Hero image - no modifications */
  .hero-image {
    /* No transform effects - keep image at true size */
  }
}