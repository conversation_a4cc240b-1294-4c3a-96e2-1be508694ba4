@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Enhanced Light Mode Color Palette */
    --background: 210 20% 98%; /* Soft Off-White #FAFBFC */
    --foreground: 222.2 84% 4.9%; /* Rich Dark Text */

    --card: 0 0% 100%; /* Pure White Cards for contrast */
    --card-foreground: 222.2 84% 4.9%; /* Rich Dark Text */

    --popover: 0 0% 100%; /* Pure White Popovers */
    --popover-foreground: 222.2 84% 4.9%; /* Rich Dark Text */

    --primary: 355 65% 36%; /* Deep Red #9B1C22 */
    --primary-foreground: 0 0% 100%; /* Pure White */

    --secondary: 210 40% 97%; /* Lighter Secondary Background */
    --secondary-foreground: 222.2 47.4% 11.2%; /* Dark Text */

    --muted: 210 40% 96%; /* Soft Muted Background */
    --muted-foreground: 215.4 16.3% 46.9%; /* Muted Text */

    --accent: 45 93% 47%; /* Elegant Gold #D4AF37 */
    --accent-foreground: 0 0% 10%; /* Dark Text on Gold */

    --destructive: 0 84.2% 60.2%; /* Red for destructive actions */
    --destructive-foreground: 210 40% 98%; /* Light text on red */

    --border: 214.3 31.8% 88%; /* Softer Borders */
    --input: 214.3 31.8% 96%; /* Light Input Background */
    --ring: 355 65% 36%; /* Primary color for focus rings */

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Enhanced Dark Mode Color Palette */
    --background: 0 0% 4%; /* Deeper Charcoal #0A0A0A */
    --foreground: 0 0% 96%; /* Brighter Off-White #F5F5F5 */

    --card: 0 0% 9%; /* Enhanced Dark Slate Grey #171717 */
    --card-foreground: 0 0% 96%; /* Brighter Off-White #F5F5F5 */

    --popover: 0 0% 9%; /* Enhanced Dark Slate Grey #171717 */
    --popover-foreground: 0 0% 96%; /* Brighter Off-White #F5F5F5 */

    --primary: 0 84% 51%; /* Enhanced Deep Red #DC2626 */
    --primary-foreground: 0 0% 96%; /* Brighter Off-White #F5F5F5 */

    --secondary: 0 0% 9%; /* Enhanced Dark Slate Grey #171717 */
    --secondary-foreground: 0 0% 96%; /* Brighter Off-White #F5F5F5 */

    --muted: 0 0% 15%; /* Lighter muted background #262626 */
    --muted-foreground: 0 0% 64%; /* Enhanced muted text #A3A3A3 */

    --accent: 43 96% 56%; /* Enhanced Illuminated Gold #F59E0B */
    --accent-foreground: 0 0% 4%; /* Deeper background #0A0A0A */

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 88%;

    --border: 0 0% 25%; /* Enhanced border visibility #404040 */
    --input: 0 0% 9%; /* Enhanced Dark Slate Grey #171717 */
    --ring: 43 96% 56%; /* Enhanced Illuminated Gold #F59E0B */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-body antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
  }

  /* Smooth theme transitions for all elements */
  * {
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  }

  /* Prevent flash of unstyled content during theme switch */
  html {
    color-scheme: light dark;
  }

  /* Enhanced focus styles for dark mode */
  .dark *:focus-visible {
    outline: 2px solid hsl(var(--accent));
    outline-offset: 2px;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Better text rendering */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
    text-wrap: balance;
  }

  p {
    text-wrap: pretty;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-deep-red hover:bg-deep-red-700 text-clean-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .btn-secondary {
    @apply bg-elegant-gold hover:bg-elegant-gold-600 text-classic-black font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-large hover:-translate-y-1;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-deep-red to-elegant-gold bg-clip-text text-transparent;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Enhanced Light mode specific styles */
  .light-gradient-bg {
    background: linear-gradient(135deg, #FAFBFC 0%, #F8FAFC 100%);
  }

  .light-card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .light-card-shadow-hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .light-border-soft {
    border-color: #E2E8F0;
  }

  /* Enhanced Dark mode specific styles */
  .dark .hero-bg {
    @apply bg-deep-red-800;
  }

  .dark .card-bg {
    @apply bg-card border-border;
  }

  .dark .text-muted {
    @apply text-muted-foreground;
  }

  /* Dark mode button styles */
  .dark .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground;
  }

  .dark .btn-secondary {
    background-color: #F0C419; /* Illuminated Gold */
    color: #121212; /* Charcoal Grey */
  }

  .dark .btn-secondary:hover {
    background-color: #E6B800; /* Slightly darker gold */
  }

  /* Dark mode card enhancements */
  .dark .card-hover {
    @apply hover:bg-card/80;
  }

  /* Enhanced dark mode gradients */
  .dark .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%);
  }

  .dark .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)/0.8) 100%);
  }

  .dark .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)/0.95) 100%);
  }

  /* Enhanced dark mode shadows */
  .dark .shadow-glow {
    box-shadow: 0 0 20px hsl(var(--accent)/0.3);
  }

  .dark .shadow-primary-glow {
    box-shadow: 0 0 20px hsl(var(--primary)/0.3);
  }

  /* Dark mode border enhancements */
  .dark .border-glow {
    border: 1px solid hsl(var(--accent)/0.5);
    box-shadow: 0 0 10px hsl(var(--accent)/0.2);
  }

  /* Dark mode text enhancements */
  .dark .text-glow {
    text-shadow: 0 0 10px hsl(var(--accent)/0.5);
  }

  /* Dark mode text gradients */
  .dark .text-gradient {
    background: linear-gradient(to right, #9B1C22, #F0C419);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Dark mode shadows */
  .dark .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-large {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 20px 25px -5px rgba(0, 0, 0, 0.3);
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hero section animations */
  .hero-float {
    animation: float 6s ease-in-out infinite;
  }

  .hero-glow {
    animation: glow 4s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }

  /* Hero image - no modifications */
  .hero-image {
    /* No transform effects - keep image at true size */
  }
}