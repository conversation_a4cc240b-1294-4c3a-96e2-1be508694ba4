@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 355 65% 36%; /* Deep Red #9B1C22 */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 45 93% 47%; /* Elegant Gold #D4AF37 */
    --accent-foreground: 0 0% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 10%; /* Dark background #1a1a1a */
    --foreground: 0 0% 98%;

    --card: 0 0% 16%; /* Dark card background #2a2a2a */
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 16%;
    --popover-foreground: 0 0% 98%;

    --primary: 355 65% 36%; /* Keep Deep Red */
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 23%; /* Dark secondary #3a3a3a */
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 16%;
    --muted-foreground: 240 5% 65%;

    --accent: 45 93% 47%; /* Keep Elegant Gold */
    --accent-foreground: 0 0% 10%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 23%;
    --input: 0 0% 23%;
    --ring: 355 65% 36%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-body antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Better text rendering */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
    text-wrap: balance;
  }

  p {
    text-wrap: pretty;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-deep-red hover:bg-deep-red-700 text-clean-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .btn-secondary {
    @apply bg-elegant-gold hover:bg-elegant-gold-600 text-classic-black font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-large hover:-translate-y-1;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-deep-red to-elegant-gold bg-clip-text text-transparent;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Dark mode specific styles */
  .dark .hero-bg {
    @apply bg-deep-red-800;
  }

  .dark .card-bg {
    @apply bg-card border-border;
  }

  .dark .text-muted {
    @apply text-muted-foreground;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}