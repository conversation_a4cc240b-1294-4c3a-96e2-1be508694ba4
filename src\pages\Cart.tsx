
import React from 'react';
import { Link } from 'react-router-dom';
import { Minus, Plus, Trash2, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useCart } from '@/context/CartContext';

const Cart = () => {
  const { items, updateQuantity, removeFromCart, getTotalPrice, getTotalItems } = useCart();

  if (items.length === 0) {
    return (
      <div className="min-h-screen gradient-surface flex items-center justify-center">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-2xl mx-auto">
            <div className="bg-muted/50 rounded-full p-8 w-fit mx-auto mb-8">
              <ShoppingBag className="h-24 w-24 text-muted-foreground mx-auto" />
            </div>
            <h1 className="text-5xl font-bold text-foreground mb-6">Your cart is empty</h1>
            <p className="text-xl text-muted-foreground mb-12">Start shopping to add items to your cart and discover amazing books</p>
            <Link to="/books">
              <Button className="btn-primary text-xl px-12 py-6">
                Browse Books
                <span className="ml-3">→</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-surface">
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <div className="badge-modern mb-6">Shopping Cart</div>
          <h1 className="text-5xl font-bold text-foreground mb-4">Your Cart</h1>
          <p className="text-xl text-muted-foreground">Review your selected books before checkout</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Modern Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            {items.map(({ book, quantity }) => (
              <Card key={book.id} className="card-modern p-8 glow-accent">
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="w-full sm:w-24 h-32 sm:h-24 bg-gray-100 dark:bg-muted rounded-lg overflow-hidden flex-shrink-0 border dark:border-border">
                    <img
                      src={book.image}
                      alt={book.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-classic-black dark:text-card-foreground">{book.title}</h3>
                        <p className="text-gray-600 dark:text-muted-foreground">{book.author}</p>
                        <p className="text-deep-red dark:text-primary font-bold">${book.price.toFixed(2)}</p>
                      </div>
                      <Button
                        onClick={() => removeFromCart(book.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => updateQuantity(book.id, quantity - 1)}
                        variant="outline"
                        size="sm"
                        disabled={quantity <= 1}
                        className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="w-12 text-center font-medium text-foreground dark:text-card-foreground">{quantity}</span>
                      <Button
                        onClick={() => updateQuantity(book.id, quantity + 1)}
                        variant="outline"
                        size="sm"
                        disabled={quantity >= book.stock}
                        className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    <p className="text-lg font-bold text-classic-black dark:text-card-foreground">
                      Subtotal: ${(book.price * quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              </CardContent>
              </Card>
            ))}
          </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4 bg-card dark:bg-card border dark:border-border shadow-soft dark:shadow-large">
            <CardContent className="p-6">
              <h2 className="text-xl font-bold text-classic-black dark:text-card-foreground mb-4">Order Summary</h2>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-foreground dark:text-card-foreground">
                  <span>Items ({getTotalItems()})</span>
                  <span>${getTotalPrice().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-foreground dark:text-card-foreground">
                  <span>Delivery</span>
                  <span className="text-green-600 dark:text-green-400">Free</span>
                </div>
                <div className="border-t dark:border-border pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-foreground dark:text-card-foreground">Total</span>
                    <span className="text-deep-red dark:text-primary">${getTotalPrice().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg mb-6 border dark:border-amber-800/30">
                <p className="text-sm text-amber-800 dark:text-amber-200 font-medium">
                  💡 Payment on Delivery Only
                </p>
                <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  You'll pay when you receive your order
                </p>
              </div>

              <Link to="/checkout" className="block">
                <Button className="w-full bg-elegant-gold dark:bg-accent hover:bg-elegant-gold/90 dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground font-bold py-3 text-lg">
                  Proceed to Checkout
                </Button>
              </Link>

              <Link to="/books" className="block mt-4">
                <Button variant="outline" className="w-full border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground">
                  Continue Shopping
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
