
import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { CheckCircle, MessageCircle, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useOrders } from '@/context/OrderContext';

const OrderConfirmation = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const { getOrderById } = useOrders();
  
  const order = orderId ? getOrderById(orderId) : null;

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-classic-black mb-4">Order not found</h1>
          <Link to="/">
            <Button className="bg-elegant-gold hover:bg-elegant-gold/90 text-classic-black">
              Go to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const whatsappMessage = `Hello! I want to confirm my order:
Order ID: ${order.id}
Customer: ${order.customerName}
Phone: ${order.customerPhone}
Address: ${order.customerAddress}
Total: $${order.total.toFixed(2)}

Items:
${order.items.map(item => `- ${item.book.title} (x${item.quantity}) - $${(item.book.price * item.quantity).toFixed(2)}`).join('\n')}

Please confirm this order. Thank you!`;

  const whatsappUrl = `https://wa.me/21629381882?text=${encodeURIComponent(whatsappMessage)}`;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="text-center mb-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-classic-black mb-2">Thank You!</h1>
          <p className="text-xl text-gray-600">Your order has been placed successfully</p>
        </div>

        {/* Order Details */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-classic-black mb-3">Order Information</h3>
                <div className="space-y-2">
                  <p><strong>Order ID:</strong> {order.id}</p>
                  <p><strong>Date:</strong> {new Date(order.createdAt).toLocaleDateString()}</p>
                  <p><strong>Status:</strong> <span className="text-orange-600">{order.status}</span></p>
                  <p><strong>Total:</strong> <span className="text-deep-red font-bold">${order.total.toFixed(2)}</span></p>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-classic-black mb-3">Delivery Information</h3>
                <div className="space-y-2">
                  <p><strong>Name:</strong> {order.customerName}</p>
                  <p><strong>Phone:</strong> {order.customerPhone}</p>
                  <p><strong>Address:</strong> {order.customerAddress}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Ordered */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <h3 className="font-semibold text-classic-black mb-4">Items Ordered</h3>
            <div className="space-y-3">
              {order.items.map(({ book, quantity }) => (
                <div key={book.id} className="flex justify-between items-center py-2 border-b border-gray-100">
                  <div>
                    <h4 className="font-medium">{book.title}</h4>
                    <p className="text-sm text-gray-600">{book.author}</p>
                    <p className="text-sm">Quantity: {quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">${(book.price * quantity).toFixed(2)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* WhatsApp Confirmation */}
        <Card className="bg-deep-red text-clean-white">
          <CardContent className="p-6 text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">IMPORTANT: Confirm Your Order on WhatsApp</h2>
            <p className="mb-6 opacity-90">
              Please confirm your order by sending us a message on WhatsApp. 
              This ensures we have all the correct details for your delivery.
            </p>
            <a href={whatsappUrl} target="_blank" rel="noopener noreferrer">
              <Button size="lg" className="bg-elegant-gold hover:bg-elegant-gold/90 text-classic-black font-bold">
                <MessageCircle className="h-5 w-5 mr-2" />
                Confirm Order on WhatsApp
              </Button>
            </a>
            <div className="mt-4 flex items-center justify-center space-x-2 text-sm opacity-80">
              <Phone className="h-4 w-4" />
              <span>+216 29 381 882</span>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <div className="mt-8 text-center">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link to="/books">
              <Button variant="outline" className="w-full">
                Continue Shopping
              </Button>
            </Link>
            <Link to="/">
              <Button variant="outline" className="w-full">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmation;
