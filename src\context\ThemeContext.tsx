import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light');

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('kotobcom-theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeState(systemPrefersDark ? 'dark' : 'light');
    }
  }, []);

  // Apply theme to document with smooth transitions
  useEffect(() => {
    const root = document.documentElement;

    // Add transition class for smooth theme switching
    root.style.setProperty('--theme-transition', 'all 0.3s ease-in-out');

    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Update CSS custom properties with official Dark Mode palette
    if (theme === 'dark') {
      // Enhanced Dark Mode Color Palette with improved contrast and visual appeal
      root.style.setProperty('--background', '#0A0A0A'); // Deeper Charcoal for better contrast
      root.style.setProperty('--foreground', '#F5F5F5'); // Brighter Off-White for better readability
      root.style.setProperty('--card', '#171717'); // Enhanced Dark Slate Grey for better depth
      root.style.setProperty('--card-foreground', '#F5F5F5'); // Brighter Off-White
      root.style.setProperty('--primary', '#DC2626'); // Enhanced Deep Red with more vibrancy
      root.style.setProperty('--primary-foreground', '#F5F5F5'); // Brighter Off-White
      root.style.setProperty('--secondary', '#171717'); // Consistent with card
      root.style.setProperty('--secondary-foreground', '#F5F5F5'); // Brighter Off-White
      root.style.setProperty('--muted', '#262626'); // Lighter muted background for better contrast
      root.style.setProperty('--muted-foreground', '#A3A3A3'); // Enhanced muted text
      root.style.setProperty('--accent', '#F59E0B'); // Enhanced Illuminated Gold with more warmth
      root.style.setProperty('--accent-foreground', '#0A0A0A'); // Deeper background for contrast
      root.style.setProperty('--border', '#404040'); // Enhanced border visibility
      root.style.setProperty('--input', '#171717'); // Consistent with card
      root.style.setProperty('--ring', '#F59E0B'); // Enhanced focus rings
    } else {
      // Light Mode (Original)
      root.style.setProperty('--background', '#ffffff');
      root.style.setProperty('--foreground', '#1a1a1a');
      root.style.setProperty('--card', '#ffffff');
      root.style.setProperty('--card-foreground', '#1a1a1a');
      root.style.setProperty('--primary', '#9B1C22');
      root.style.setProperty('--primary-foreground', '#ffffff');
      root.style.setProperty('--secondary', '#f1f5f9');
      root.style.setProperty('--secondary-foreground', '#1a1a1a');
      root.style.setProperty('--muted', '#f1f5f9');
      root.style.setProperty('--muted-foreground', '#64748b');
      root.style.setProperty('--accent', '#D4AF37');
      root.style.setProperty('--accent-foreground', '#1a1a1a');
      root.style.setProperty('--border', '#e2e8f0');
      root.style.setProperty('--input', '#e2e8f0');
      root.style.setProperty('--ring', '#9B1C22');
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('kotobcom-theme', newTheme);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
