import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light');

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('kotobcom-theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeState(systemPrefersDark ? 'dark' : 'light');
    }
  }, []);

  // Apply theme to document with smooth transitions
  useEffect(() => {
    const root = document.documentElement;

    // Add transition class for smooth theme switching
    root.style.setProperty('--theme-transition', 'all 0.3s ease-in-out');

    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Update CSS custom properties with official Dark Mode palette
    if (theme === 'dark') {
      // Modern Dark Mode - Sophisticated & Premium Design System
      root.style.setProperty('--background', '#0F172A'); // Rich Dark Navy
      root.style.setProperty('--foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--card', '#1E293B'); // Elevated Dark Surface
      root.style.setProperty('--card-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--primary', '#E11D48'); // Modern Crimson
      root.style.setProperty('--primary-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--secondary', '#334155'); // Dark Secondary
      root.style.setProperty('--secondary-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--muted', '#334155'); // Subtle Dark Background
      root.style.setProperty('--muted-foreground', '#94A3B8'); // Muted Text
      root.style.setProperty('--accent', '#F59E0B'); // Vibrant Gold
      root.style.setProperty('--accent-foreground', '#0F172A'); // Rich Dark Navy
      root.style.setProperty('--success', '#059669'); // Fresh Green
      root.style.setProperty('--success-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--warning', '#F97316'); // Vibrant Orange
      root.style.setProperty('--warning-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--destructive', '#EF4444'); // Modern Red
      root.style.setProperty('--destructive-foreground', '#F8FAFC'); // Crisp White
      root.style.setProperty('--border', '#334155'); // Subtle Dark Border
      root.style.setProperty('--input', '#1E293B'); // Input Background
      root.style.setProperty('--ring', '#F59E0B'); // Vibrant Gold Focus Ring
    } else {
      // Modern Light Mode - Sophisticated & Premium Design System
      root.style.setProperty('--background', '#F8F9FB'); // Sophisticated Light Grey
      root.style.setProperty('--foreground', '#0F172A'); // Rich Charcoal
      root.style.setProperty('--card', '#FFFFFF'); // Pure White Cards
      root.style.setProperty('--card-foreground', '#0F172A'); // Rich Charcoal
      root.style.setProperty('--primary', '#E11D48'); // Modern Crimson
      root.style.setProperty('--primary-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--secondary', '#EBEEF3'); // Light Secondary
      root.style.setProperty('--secondary-foreground', '#0F172A'); // Rich Charcoal
      root.style.setProperty('--muted', '#EBEEF3'); // Subtle Background
      root.style.setProperty('--muted-foreground', '#6B7280'); // Muted Text
      root.style.setProperty('--accent', '#F59E0B'); // Warm Amber
      root.style.setProperty('--accent-foreground', '#0F172A'); // Rich Charcoal
      root.style.setProperty('--success', '#059669'); // Fresh Green
      root.style.setProperty('--success-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--warning', '#F97316'); // Vibrant Orange
      root.style.setProperty('--warning-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--destructive', '#EF4444'); // Modern Red
      root.style.setProperty('--destructive-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--border', '#E2E8F0'); // Soft Border
      root.style.setProperty('--input', '#E2E8F0'); // Input Background
      root.style.setProperty('--ring', '#E11D48'); // Primary Focus Ring
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('kotobcom-theme', newTheme);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
