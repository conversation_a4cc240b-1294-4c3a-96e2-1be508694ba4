import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light');

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('kotobcom-theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeState(systemPrefersDark ? 'dark' : 'light');
    }
  }, []);

  // Apply theme to document with smooth transitions
  useEffect(() => {
    const root = document.documentElement;

    // Add transition class for smooth theme switching
    root.style.setProperty('--theme-transition', 'all 0.3s ease-in-out');

    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Update CSS custom properties with official Dark Mode palette
    if (theme === 'dark') {
      // Harmonious Dark Mode - Warm & Sophisticated Bookstore Theme
      root.style.setProperty('--background', '#0D1117'); // Deep Charcoal
      root.style.setProperty('--foreground', '#F0F6FC'); // Soft White
      root.style.setProperty('--card', '#161B22'); // Elevated Surface
      root.style.setProperty('--card-foreground', '#F0F6FC'); // Soft White
      root.style.setProperty('--primary', '#B91C1C'); // Deep Book Red
      root.style.setProperty('--primary-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--secondary', '#21262D'); // Warm Grey
      root.style.setProperty('--secondary-foreground', '#F0F6FC'); // Soft White
      root.style.setProperty('--muted', '#21262D'); // Muted Background
      root.style.setProperty('--muted-foreground', '#8B949E'); // Muted Text
      root.style.setProperty('--accent', '#D97706'); // Warm Amber Gold
      root.style.setProperty('--accent-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--success', '#059669'); // Forest Green
      root.style.setProperty('--success-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--warning', '#D97706'); // Consistent Amber
      root.style.setProperty('--warning-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--destructive', '#DC2626'); // Consistent Red
      root.style.setProperty('--destructive-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--border', '#30363D'); // Subtle Border
      root.style.setProperty('--input', '#161B22'); // Input Background
      root.style.setProperty('--ring', '#D97706'); // Amber Focus Ring
    } else {
      // Harmonious Light Mode - Warm & Elegant Bookstore Theme
      root.style.setProperty('--background', '#FEFEFE'); // Pure White
      root.style.setProperty('--foreground', '#1F2937'); // Rich Dark Grey
      root.style.setProperty('--card', '#FFFFFF'); // Pure White Cards
      root.style.setProperty('--card-foreground', '#1F2937'); // Rich Dark Grey
      root.style.setProperty('--primary', '#B91C1C'); // Deep Book Red
      root.style.setProperty('--primary-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--secondary', '#F9FAFB'); // Light Grey
      root.style.setProperty('--secondary-foreground', '#1F2937'); // Rich Dark Grey
      root.style.setProperty('--muted', '#F3F4F6'); // Soft Muted
      root.style.setProperty('--muted-foreground', '#6B7280'); // Muted Text
      root.style.setProperty('--accent', '#D97706'); // Warm Amber Gold
      root.style.setProperty('--accent-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--success', '#059669'); // Forest Green
      root.style.setProperty('--success-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--warning', '#D97706'); // Consistent Amber
      root.style.setProperty('--warning-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--destructive', '#DC2626'); // Consistent Red
      root.style.setProperty('--destructive-foreground', '#FFFFFF'); // Pure White
      root.style.setProperty('--border', '#E5E7EB'); // Soft Border
      root.style.setProperty('--input', '#F9FAFB'); // Input Background
      root.style.setProperty('--ring', '#D97706'); // Amber Focus Ring
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('kotobcom-theme', newTheme);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
