import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light');

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('kotobcom-theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Check system preference
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeState(systemPrefersDark ? 'dark' : 'light');
    }
  }, []);

  // Apply theme to document with smooth transitions
  useEffect(() => {
    const root = document.documentElement;

    // Add transition class for smooth theme switching
    root.style.setProperty('--theme-transition', 'all 0.3s ease-in-out');

    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Update CSS custom properties with official Dark Mode palette
    if (theme === 'dark') {
      // Official Dark Mode Color Palette
      root.style.setProperty('--background', '#121212'); // Charcoal Grey
      root.style.setProperty('--foreground', '#E1E1E1'); // Off-White
      root.style.setProperty('--card', '#1E1E1E'); // Dark Slate Grey
      root.style.setProperty('--card-foreground', '#E1E1E1'); // Off-White
      root.style.setProperty('--primary', '#9B1C22'); // Deep Red (Brand Identity)
      root.style.setProperty('--primary-foreground', '#E1E1E1'); // Off-White
      root.style.setProperty('--secondary', '#1E1E1E'); // Dark Slate Grey
      root.style.setProperty('--secondary-foreground', '#E1E1E1'); // Off-White
      root.style.setProperty('--muted', '#1E1E1E'); // Dark Slate Grey
      root.style.setProperty('--muted-foreground', '#a1a1aa'); // Muted text
      root.style.setProperty('--accent', '#F0C419'); // Illuminated Gold
      root.style.setProperty('--accent-foreground', '#121212'); // Charcoal Grey
      root.style.setProperty('--border', '#333333'); // Subtle borders
      root.style.setProperty('--input', '#1E1E1E'); // Dark Slate Grey
      root.style.setProperty('--ring', '#F0C419'); // Illuminated Gold
    } else {
      // Light Mode (Original)
      root.style.setProperty('--background', '#ffffff');
      root.style.setProperty('--foreground', '#1a1a1a');
      root.style.setProperty('--card', '#ffffff');
      root.style.setProperty('--card-foreground', '#1a1a1a');
      root.style.setProperty('--primary', '#9B1C22');
      root.style.setProperty('--primary-foreground', '#ffffff');
      root.style.setProperty('--secondary', '#f1f5f9');
      root.style.setProperty('--secondary-foreground', '#1a1a1a');
      root.style.setProperty('--muted', '#f1f5f9');
      root.style.setProperty('--muted-foreground', '#64748b');
      root.style.setProperty('--accent', '#D4AF37');
      root.style.setProperty('--accent-foreground', '#1a1a1a');
      root.style.setProperty('--border', '#e2e8f0');
      root.style.setProperty('--input', '#e2e8f0');
      root.style.setProperty('--ring', '#9B1C22');
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('kotobcom-theme', newTheme);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
