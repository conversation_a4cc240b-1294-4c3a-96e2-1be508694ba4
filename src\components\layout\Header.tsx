
import React from 'react';
import { Link } from 'react-router-dom';
import { Shopping<PERSON>art, BookOpen, Heart } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useBooks } from '@/context/BookContext';
import SearchBar from '@/components/ui/search-bar';

const Header = () => {
  const { getTotalItems } = useCart();
  const { getAllCategories } = useBooks();
  const totalItems = getTotalItems();
  const categories = getAllCategories().slice(0, 4); // Show top 4 categories

  return (
    <header className="bg-deep-red text-clean-white shadow-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between gap-4">
          <Link to="/" className="flex items-center space-x-2 hover:text-elegant-gold transition-colors">
            <BookOpen className="h-8 w-8" />
            <span className="text-2xl font-bold">Kotobcom</span>
          </Link>
          
          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-4">
            <SearchBar />
          </div>
          
          <div className="flex items-center space-x-4">
            <Link to="/cart" className="relative flex items-center space-x-2 hover:text-elegant-gold transition-colors">
              <ShoppingCart className="h-6 w-6" />
              <span className="hidden sm:inline">Cart</span>
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-elegant-gold text-classic-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                  {totalItems}
                </span>
              )}
            </Link>
          </div>
        </div>
        
        {/* Enhanced Navigation */}
        <nav className="mt-4 flex flex-wrap items-center justify-center gap-6">
          <Link to="/" className="hover:text-elegant-gold transition-colors font-medium">Home</Link>
          <Link to="/books" className="hover:text-elegant-gold transition-colors font-medium">All Books</Link>
          {categories.map(category => (
            <Link 
              key={category} 
              to={`/books?category=${category}`} 
              className="hover:text-elegant-gold transition-colors font-medium"
            >
              {category}
            </Link>
          ))}
          <Link to="/books?featured=true" className="hover:text-elegant-gold transition-colors font-medium flex items-center">
            <Heart className="h-4 w-4 mr-1" />
            Featured
          </Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;
