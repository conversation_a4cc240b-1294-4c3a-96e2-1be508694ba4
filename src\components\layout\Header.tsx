
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shopping<PERSON>art, BookOpen, Heart, Menu, X, User } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useBooks } from '@/context/BookContext';
import SearchBar from '@/components/ui/search-bar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const Header = () => {
  const { getTotalItems } = useCart();
  const { getAllCategories } = useBooks();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const totalItems = getTotalItems();
  const categories = getAllCategories().slice(0, 3); // Show top 3 categories

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-deep-red text-clean-white sticky top-0 z-50">
      {/* Single Unified Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between gap-6">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2 hover:text-elegant-gold transition-colors">
            <BookOpen className="h-8 w-8" />
            <span className="text-2xl font-bold">Kotobcom</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link to="/" className="text-clean-white hover:text-elegant-gold transition-colors font-medium">
              Home
            </Link>
            <Link to="/books" className="text-clean-white hover:text-elegant-gold transition-colors font-medium">
              All Books
            </Link>
            {categories.map(category => (
              <Link
                key={category}
                to={`/books?category=${category}`}
                className="text-clean-white hover:text-elegant-gold transition-colors font-medium"
              >
                {category}
              </Link>
            ))}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md">
            <SearchBar />
          </div>

          {/* Cart */}
          <Link to="/cart" className="relative flex items-center space-x-2 hover:text-elegant-gold transition-colors">
            <ShoppingCart className="h-6 w-6" />
            <span className="hidden sm:inline">Cart</span>
            {totalItems > 0 && (
              <span className="absolute -top-2 -right-2 bg-elegant-gold text-classic-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                {totalItems}
              </span>
            )}
          </Link>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMobileMenu}
            className="lg:hidden text-clean-white hover:text-elegant-gold hover:bg-clean-white/10"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden mt-4">
          <SearchBar />
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-deep-red-900 border-t border-clean-white/10">
          <nav className="container mx-auto px-4 py-4 space-y-3">
            <Link
              to="/"
              className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/books"
              className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              All Books
            </Link>
            {categories.map(category => (
              <Link
                key={category}
                to={`/books?category=${category}`}
                className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {category}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
