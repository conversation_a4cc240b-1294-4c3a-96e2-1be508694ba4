
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Shopping<PERSON>art, BookOpen, Heart, Menu, X, User } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useBooks } from '@/context/BookContext';
import SearchBar from '@/components/ui/search-bar';
import ThemeToggle from '@/components/ui/theme-toggle';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const Header = () => {
  const { getTotalItems } = useCart();
  const { getAllCategories } = useBooks();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const totalItems = getTotalItems();
  const categories = getAllCategories().slice(0, 3); // Show top 3 categories

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="gradient-primary text-primary-foreground sticky top-0 z-50 backdrop-blur-sm border-b border-primary-foreground/10 shadow-xl">
      {/* Modern Unified Header */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between gap-8">
          {/* Modern Logo */}
          <Link to="/" className="flex items-center space-x-3 hover:scale-105 transition-all duration-300 group">
            <div className="bg-accent/20 p-3 rounded-2xl group-hover:bg-accent/30 transition-colors duration-300 glow-accent">
              <BookOpen className="h-8 w-8 text-accent" />
            </div>
            <span className="text-3xl font-bold tracking-tight">Kotobcom</span>
          </Link>

          {/* Modern Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link to="/" className="text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg hover:scale-105">
              Home
            </Link>
            <Link to="/books" className="text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg hover:scale-105">
              All Books
            </Link>
            {categories.map(category => (
              <Link
                key={category}
                to={`/books?category=${category}`}
                className="text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg hover:scale-105"
              >
                {category}
              </Link>
            ))}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md">
            <SearchBar />
          </div>

          {/* Modern Theme Toggle & Cart */}
          <div className="flex items-center space-x-6">
            <ThemeToggle />
            <Link to="/cart" className="relative flex items-center space-x-3 hover:text-accent transition-all duration-300 hover:scale-105 group">
              <div className="bg-primary-foreground/10 p-2 rounded-xl group-hover:bg-accent/20 transition-colors duration-300">
                <ShoppingCart className="h-6 w-6" />
              </div>
              <span className="hidden sm:inline font-semibold text-lg">Cart</span>
              {totalItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-accent text-accent-foreground text-sm rounded-full h-6 w-6 flex items-center justify-center font-bold glow-accent">
                  {totalItems}
                </span>
              )}
            </Link>
          </div>

          {/* Modern Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMobileMenu}
            className="lg:hidden text-primary-foreground hover:text-accent hover:bg-primary-foreground/10 rounded-xl p-3"
          >
            {isMobileMenuOpen ? <X className="h-7 w-7" /> : <Menu className="h-7 w-7" />}
          </Button>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden mt-4">
          <SearchBar />
        </div>
      </div>

      {/* Modern Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-primary/95 backdrop-blur-sm border-t border-primary-foreground/20">
          <nav className="container mx-auto px-4 py-6 space-y-4">
            <Link
              to="/"
              className="block text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg py-3 px-4 rounded-xl hover:bg-primary-foreground/10"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/books"
              className="block text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg py-3 px-4 rounded-xl hover:bg-primary-foreground/10"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              All Books
            </Link>
            {categories.map(category => (
              <Link
                key={category}
                to={`/books?category=${category}`}
                className="block text-primary-foreground hover:text-accent transition-all duration-300 font-semibold text-lg py-3 px-4 rounded-xl hover:bg-primary-foreground/10"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {category}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
