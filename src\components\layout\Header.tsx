
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Shopping<PERSON>art, BookOpen, Heart, Menu, X, User } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useBooks } from '@/context/BookContext';
import SearchBar from '@/components/ui/search-bar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const Header = () => {
  const { getTotalItems } = useCart();
  const { getAllCategories } = useBooks();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const totalItems = getTotalItems();
  const categories = getAllCategories().slice(0, 4); // Show top 4 categories

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-gradient-to-r from-deep-red to-deep-red-800 text-clean-white shadow-large sticky top-0 z-50 backdrop-blur-sm">
      {/* Top Bar */}
      <div className="bg-deep-red-900/50 border-b border-clean-white/10">
        <div className="container mx-auto container-padding py-2">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span className="text-clean-white/80">📞 +216 29 381 882</span>
              <span className="hidden md:inline text-clean-white/80">🚚 Free delivery on orders over $50</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/admin/login" className="text-clean-white/80 hover:text-elegant-gold transition-colors">
                <User className="h-4 w-4 inline mr-1" />
                Admin
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto container-padding py-4">
        <div className="flex items-center justify-between gap-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 hover:text-elegant-gold transition-all duration-300 group">
            <div className="bg-elegant-gold p-2 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <BookOpen className="h-8 w-8 text-classic-black" />
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold tracking-tight">Kotobcom</span>
              <span className="text-xs text-clean-white/70 hidden sm:block">Your Trusted Bookstore</span>
            </div>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <SearchBar />
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/cart" className="relative group">
              <Button variant="ghost" className="text-clean-white hover:text-elegant-gold hover:bg-clean-white/10 transition-all duration-300">
                <ShoppingCart className="h-5 w-5 mr-2" />
                <span>Cart</span>
                {totalItems > 0 && (
                  <Badge className="absolute -top-2 -right-2 bg-elegant-gold text-classic-black text-xs h-5 w-5 flex items-center justify-center p-0 group-hover:scale-110 transition-transform">
                    {totalItems}
                  </Badge>
                )}
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-2">
            <Link to="/cart" className="relative">
              <Button variant="ghost" size="icon" className="text-clean-white hover:text-elegant-gold hover:bg-clean-white/10">
                <ShoppingCart className="h-5 w-5" />
                {totalItems > 0 && (
                  <Badge className="absolute -top-1 -right-1 bg-elegant-gold text-classic-black text-xs h-4 w-4 flex items-center justify-center p-0">
                    {totalItems}
                  </Badge>
                )}
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMobileMenu}
              className="text-clean-white hover:text-elegant-gold hover:bg-clean-white/10"
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden mt-4">
          <SearchBar />
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center justify-center mt-6 space-x-8">
          <Link to="/" className="text-clean-white hover:text-elegant-gold transition-all duration-300 font-medium relative group">
            Home
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-elegant-gold transition-all duration-300 group-hover:w-full"></span>
          </Link>
          <Link to="/books" className="text-clean-white hover:text-elegant-gold transition-all duration-300 font-medium relative group">
            All Books
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-elegant-gold transition-all duration-300 group-hover:w-full"></span>
          </Link>
          {categories.map(category => (
            <Link
              key={category}
              to={`/books?category=${category}`}
              className="text-clean-white hover:text-elegant-gold transition-all duration-300 font-medium relative group"
            >
              {category}
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-elegant-gold transition-all duration-300 group-hover:w-full"></span>
            </Link>
          ))}
          <Link to="/books?featured=true" className="text-clean-white hover:text-elegant-gold transition-all duration-300 font-medium flex items-center relative group">
            <Heart className="h-4 w-4 mr-1" />
            Featured
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-elegant-gold transition-all duration-300 group-hover:w-full"></span>
          </Link>
        </nav>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-deep-red-900/95 backdrop-blur-sm border-t border-clean-white/10 animate-fade-in-up">
          <nav className="container mx-auto container-padding py-6 space-y-4">
            <Link
              to="/"
              className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/books"
              className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              All Books
            </Link>
            {categories.map(category => (
              <Link
                key={category}
                to={`/books?category=${category}`}
                className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {category}
              </Link>
            ))}
            <Link
              to="/books?featured=true"
              className="block text-clean-white hover:text-elegant-gold transition-colors font-medium py-2 flex items-center"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <Heart className="h-4 w-4 mr-2" />
              Featured
            </Link>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
