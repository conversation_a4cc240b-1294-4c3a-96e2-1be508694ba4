import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  isLoading: boolean;
}

export const usePerformance = (componentName?: string) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    isLoading: true,
  });

  useEffect(() => {
    const startTime = performance.now();

    // Mark the start of component render
    if (componentName) {
      performance.mark(`${componentName}-start`);
    }

    const handleLoad = () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;

      if (componentName) {
        performance.mark(`${componentName}-end`);
        performance.measure(`${componentName}-render`, `${componentName}-start`, `${componentName}-end`);
      }

      setMetrics({
        loadTime,
        renderTime: endTime - startTime,
        isLoading: false,
      });
    };

    // Use requestAnimationFrame to measure after render
    const rafId = requestAnimationFrame(handleLoad);

    return () => {
      cancelAnimationFrame(rafId);
    };
  }, [componentName]);

  return metrics;
};

// Hook for measuring Core Web Vitals
export const useWebVitals = () => {
  const [vitals, setVitals] = useState({
    lcp: 0, // Largest Contentful Paint
    fid: 0, // First Input Delay
    cls: 0, // Cumulative Layout Shift
  });

  useEffect(() => {
    // LCP Observer
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number };
      setVitals(prev => ({ ...prev, lcp: lastEntry.startTime }));
    });

    // CLS Observer
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      setVitals(prev => ({ ...prev, cls: clsValue }));
    });

    // FID Observer
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        setVitals(prev => ({ ...prev, fid: (entry as any).processingStart - entry.startTime }));
      }
    });

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }

    return () => {
      lcpObserver.disconnect();
      clsObserver.disconnect();
      fidObserver.disconnect();
    };
  }, []);

  return vitals;
};

// Hook for preloading resources
export const usePreload = () => {
  const preloadImage = (src: string, priority: 'high' | 'low' = 'low') => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    link.fetchPriority = priority;
    document.head.appendChild(link);
  };

  const preloadFont = (href: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'font';
    link.type = 'font/woff2';
    link.href = href;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  };

  const preloadScript = (src: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = src;
    document.head.appendChild(link);
  };

  return {
    preloadImage,
    preloadFont,
    preloadScript,
  };
};

// Hook for debouncing expensive operations
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Hook for throttling operations
export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useState(Date.now())[0];

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan >= limit) {
        setThrottledValue(value);
      }
    }, limit - (Date.now() - lastRan));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit, lastRan]);

  return throttledValue;
};
