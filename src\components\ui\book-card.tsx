
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Book } from '@/types';
import { useCart } from '@/context/CartContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Heart, Star, Eye, ShoppingCart, AlertCircle } from 'lucide-react';

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const { addToCart } = useCart();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(book);
    toast({
      title: "Added to Cart! 🛒",
      description: `${book.title} has been added to your cart.`,
    });
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    toast({
      title: isWishlisted ? "Removed from Wishlist" : "Added to Wishlist! ❤️",
      description: `${book.title} ${isWishlisted ? 'removed from' : 'added to'} your wishlist.`,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  };

  const getStockStatus = () => {
    if (book.stock === 0) return { text: 'Out of Stock', color: 'bg-red-500', textColor: 'text-red-600' };
    if (book.stock < 5) return { text: `Only ${book.stock} left`, color: 'bg-orange-500', textColor: 'text-orange-600' };
    return { text: 'In Stock', color: 'bg-green-500', textColor: 'text-green-600' };
  };

  const stockStatus = getStockStatus();
  const rating = 4.5; // Mock rating - in real app this would come from book data

  return (
    <div className="group relative" role="article" aria-labelledby={`book-title-${book.id}`}>
      <Card className="h-full card-modern overflow-hidden glow-accent group-hover:glow-primary">
        {/* Image Container */}
        <div className="relative">
          <Link
            to={`/book/${book.id}`}
            aria-label={`View details for ${book.title} by ${book.author}`}
            className="focus:outline-none focus:ring-2 focus:ring-deep-red dark:focus:ring-accent focus:ring-offset-2 dark:focus:ring-offset-card rounded-lg"
          >
            <div className="aspect-[3/4] overflow-hidden bg-warm-gray-100 dark:bg-muted">
              {!imageLoaded && (
                <div
                  className="w-full h-full bg-gradient-to-br from-warm-gray-200 to-warm-gray-300 animate-shimmer bg-[length:200%_100%] bg-gradient-to-r from-warm-gray-200 via-warm-gray-100 to-warm-gray-200"
                  aria-label="Loading book cover"
                ></div>
              )}
              <img
                src={book.image}
                alt={`Cover of ${book.title} by ${book.author}`}
                className={`w-full h-full object-cover transition-all duration-500 group-hover:scale-110 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                loading="lazy"
              />

              {/* Overlay on Hover */}
              <div className="absolute inset-0 bg-classic-black/0 group-hover:bg-classic-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-clean-white/90 hover:bg-clean-white text-classic-black shadow-medium"
                    aria-label={`Quick preview of ${book.title}`}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">Quick Preview</span>
                  </Button>
                </div>
              </div>
            </div>
          </Link>

          {/* Modern Badges */}
          <div className="absolute top-4 left-4 flex flex-col gap-3">
            {book.featured && (
              <div className="badge-modern bg-accent/90 text-accent-foreground backdrop-blur-sm">
                ⭐ FEATURED
              </div>
            )}
            {book.stock < 5 && book.stock > 0 && (
              <div className="badge-warning bg-warning/90 text-warning-foreground backdrop-blur-sm">
                ⚠️ LOW STOCK
              </div>
            )}
            {book.stock === 0 && (
              <div className="badge-modern bg-destructive/90 text-destructive-foreground backdrop-blur-sm">
                ❌ OUT OF STOCK
              </div>
            )}
          </div>

          {/* Modern Wishlist Button */}
          <Button
            size="sm"
            variant="ghost"
            onClick={handleWishlist}
            onKeyDown={(e) => handleKeyDown(e, handleWishlist)}
            aria-label={isWishlisted ? `Remove ${book.title} from wishlist` : `Add ${book.title} to wishlist`}
            aria-pressed={isWishlisted}
            className={`absolute top-4 right-4 p-3 rounded-2xl transition-all duration-300 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 backdrop-blur-sm ${
              isWishlisted
                ? 'bg-destructive/90 text-destructive-foreground hover:bg-destructive glow-accent'
                : 'bg-background/80 text-muted-foreground hover:bg-accent/20 hover:text-accent'
            } opacity-0 group-hover:opacity-100 shadow-xl`}
          >
            <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
            <span className="sr-only">
              {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
            </span>
          </Button>
        </div>

        {/* Modern Content */}
        <CardContent className="p-8 flex-1 flex flex-col">
          <Link to={`/book/${book.id}`} className="flex-1">
            {/* Modern Rating */}
            <div className="flex items-center mb-4">
              <div className="flex items-center bg-accent/10 rounded-full px-3 py-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(rating)
                        ? 'text-accent fill-current'
                        : 'text-muted'
                    }`}
                  />
                ))}
                <span className="text-sm text-muted-foreground ml-2 font-medium">({rating})</span>
              </div>
            </div>

            {/* Modern Title */}
            <h3
              id={`book-title-${book.id}`}
              className="font-bold text-card-foreground text-xl mb-3 line-clamp-2 group-hover:text-primary transition-colors leading-tight"
            >
              {book.title}
            </h3>

            {/* Modern Author */}
            <p className="text-muted-foreground mb-4 text-base font-medium">{book.author}</p>

            {/* Modern Category */}
            <div className="mb-4">
              <div className="badge-modern text-sm">
                {book.category}
              </div>
            </div>

            {/* Modern Price */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <span className="text-3xl font-bold text-primary">${book.price.toFixed(2)}</span>
                <span className="text-base text-muted-foreground line-through">${(book.price * 1.2).toFixed(2)}</span>
              </div>
              <div className="badge-success text-sm">
                Save 20%
              </div>
            </div>

            {/* Stock Status */}
            <div className="flex items-center mb-4">
              <div className={`w-2 h-2 rounded-full ${stockStatus.color} mr-2`}></div>
              <span className={`text-xs font-medium ${stockStatus.textColor}`}>
                {stockStatus.text}
              </span>
            </div>
          </Link>
        </CardContent>

        {/* Modern Footer */}
        <CardFooter className="p-8 pt-0">
          <div className="w-full space-y-3">
            <Button
              onClick={handleAddToCart}
              onKeyDown={(e) => handleKeyDown(e, () => handleAddToCart(e as any))}
              disabled={book.stock === 0}
              aria-label={book.stock === 0 ? `${book.title} is out of stock` : `Add ${book.title} to cart for $${book.price.toFixed(2)}`}
              className="btn-secondary w-full text-lg py-4 group disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ShoppingCart className="h-5 w-5 mr-3 group-hover:scale-110 transition-transform" />
              {book.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
            </Button>

            {book.stock === 0 && (
              <div className="flex items-center justify-center text-sm text-muted-foreground bg-muted/50 rounded-xl py-2">
                <AlertCircle className="h-4 w-4 mr-2" />
                Notify when available
              </div>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BookCard;
