
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Book } from '@/types';
import { useCart } from '@/context/CartContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { Heart, Star, Eye, ShoppingCart, AlertCircle } from 'lucide-react';

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const { addToCart } = useCart();
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(book);
    toast({
      title: "Added to Cart! 🛒",
      description: `${book.title} has been added to your cart.`,
    });
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    toast({
      title: isWishlisted ? "Removed from Wishlist" : "Added to Wishlist! ❤️",
      description: `${book.title} ${isWishlisted ? 'removed from' : 'added to'} your wishlist.`,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      action();
    }
  };

  const getStockStatus = () => {
    if (book.stock === 0) return { text: 'Out of Stock', color: 'bg-red-500', textColor: 'text-red-600' };
    if (book.stock < 5) return { text: `Only ${book.stock} left`, color: 'bg-orange-500', textColor: 'text-orange-600' };
    return { text: 'In Stock', color: 'bg-green-500', textColor: 'text-green-600' };
  };

  const stockStatus = getStockStatus();
  const rating = 4.5; // Mock rating - in real app this would come from book data

  return (
    <div className="group relative" role="article" aria-labelledby={`book-title-${book.id}`}>
      <Card className="h-full card-hover border-0 shadow-soft bg-clean-white overflow-hidden">
        {/* Image Container */}
        <div className="relative">
          <Link
            to={`/book/${book.id}`}
            aria-label={`View details for ${book.title} by ${book.author}`}
            className="focus:outline-none focus:ring-2 focus:ring-deep-red focus:ring-offset-2 rounded-lg"
          >
            <div className="aspect-[3/4] overflow-hidden bg-warm-gray-100">
              {!imageLoaded && (
                <div
                  className="w-full h-full bg-gradient-to-br from-warm-gray-200 to-warm-gray-300 animate-shimmer bg-[length:200%_100%] bg-gradient-to-r from-warm-gray-200 via-warm-gray-100 to-warm-gray-200"
                  aria-label="Loading book cover"
                ></div>
              )}
              <img
                src={book.image}
                alt={`Cover of ${book.title} by ${book.author}`}
                className={`w-full h-full object-cover transition-all duration-500 group-hover:scale-110 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                loading="lazy"
              />

              {/* Overlay on Hover */}
              <div className="absolute inset-0 bg-classic-black/0 group-hover:bg-classic-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-clean-white/90 hover:bg-clean-white text-classic-black shadow-medium"
                    aria-label={`Quick preview of ${book.title}`}
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">Quick Preview</span>
                  </Button>
                </div>
              </div>
            </div>
          </Link>

          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {book.featured && (
              <Badge className="bg-elegant-gold text-classic-black font-bold text-xs px-2 py-1">
                FEATURED
              </Badge>
            )}
            {book.stock < 5 && book.stock > 0 && (
              <Badge className="bg-orange-500 text-clean-white font-bold text-xs px-2 py-1">
                LOW STOCK
              </Badge>
            )}
            {book.stock === 0 && (
              <Badge className="bg-red-500 text-clean-white font-bold text-xs px-2 py-1">
                OUT OF STOCK
              </Badge>
            )}
          </div>

          {/* Wishlist Button */}
          <Button
            size="sm"
            variant="ghost"
            onClick={handleWishlist}
            onKeyDown={(e) => handleKeyDown(e, handleWishlist)}
            aria-label={isWishlisted ? `Remove ${book.title} from wishlist` : `Add ${book.title} to wishlist`}
            aria-pressed={isWishlisted}
            className={`absolute top-3 right-3 p-2 rounded-full transition-all duration-300 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-deep-red focus:ring-offset-2 ${
              isWishlisted
                ? 'bg-red-500 text-clean-white hover:bg-red-600'
                : 'bg-clean-white/80 text-warm-gray-600 hover:bg-clean-white hover:text-red-500'
            } opacity-0 group-hover:opacity-100 shadow-medium`}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
            <span className="sr-only">
              {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
            </span>
          </Button>
        </div>

        {/* Content */}
        <CardContent className="p-6 flex-1 flex flex-col">
          <Link to={`/book/${book.id}`} className="flex-1">
            {/* Rating */}
            <div className="flex items-center mb-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < Math.floor(rating)
                        ? 'text-elegant-gold fill-current'
                        : 'text-warm-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-warm-gray-500 ml-2">({rating})</span>
            </div>

            {/* Title */}
            <h3
              id={`book-title-${book.id}`}
              className="font-bold text-classic-black text-lg mb-2 line-clamp-2 group-hover:text-deep-red transition-colors leading-tight"
            >
              {book.title}
            </h3>

            {/* Author */}
            <p className="text-warm-gray-600 mb-3 text-sm font-medium">{book.author}</p>

            {/* Category */}
            <div className="mb-3">
              <Badge variant="outline" className="text-xs text-warm-gray-500 border-warm-gray-200">
                {book.category}
              </Badge>
            </div>

            {/* Price */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold text-deep-red">${book.price.toFixed(2)}</span>
                {/* Mock original price for discount effect */}
                <span className="text-sm text-warm-gray-400 line-through">${(book.price * 1.2).toFixed(2)}</span>
              </div>
              <Badge className="bg-green-100 text-green-700 text-xs">
                Save 20%
              </Badge>
            </div>

            {/* Stock Status */}
            <div className="flex items-center mb-4">
              <div className={`w-2 h-2 rounded-full ${stockStatus.color} mr-2`}></div>
              <span className={`text-xs font-medium ${stockStatus.textColor}`}>
                {stockStatus.text}
              </span>
            </div>
          </Link>
        </CardContent>

        {/* Footer */}
        <CardFooter className="p-6 pt-0">
          <div className="w-full space-y-2">
            <Button
              onClick={handleAddToCart}
              onKeyDown={(e) => handleKeyDown(e, () => handleAddToCart(e as any))}
              disabled={book.stock === 0}
              aria-label={book.stock === 0 ? `${book.title} is out of stock` : `Add ${book.title} to cart for $${book.price.toFixed(2)}`}
              className="w-full btn-secondary group disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-deep-red focus:ring-offset-2"
            >
              <ShoppingCart className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
              {book.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
            </Button>

            {book.stock === 0 && (
              <div className="flex items-center justify-center text-xs text-warm-gray-500">
                <AlertCircle className="h-3 w-3 mr-1" />
                Notify when available
              </div>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BookCard;
