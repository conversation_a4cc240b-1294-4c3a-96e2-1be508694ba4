
import React from 'react';
import { Link } from 'react-router-dom';
import { Book } from '@/types';
import { useCart } from '@/context/CartContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';

interface BookCardProps {
  book: Book;
}

const BookCard: React.FC<BookCardProps> = ({ book }) => {
  const { addToCart } = useCart();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(book);
    toast({
      title: "Added to Cart",
      description: `${book.title} has been added to your cart.`,
    });
  };

  return (
    <Link to={`/book/${book.id}`}>
      <Card className="h-full hover:shadow-lg transition-shadow duration-300 group cursor-pointer">
        <CardContent className="p-4">
          <div className="aspect-[3/4] mb-4 overflow-hidden rounded-lg">
            <img
              src={book.image}
              alt={book.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <h3 className="font-semibold text-classic-black text-lg mb-2 line-clamp-2">
            {book.title}
          </h3>
          <p className="text-gray-600 mb-2">{book.author}</p>
          <p className="text-deep-red font-bold text-xl">${book.price.toFixed(2)}</p>
          {book.stock < 5 && book.stock > 0 && (
            <p className="text-orange-500 text-sm mt-1">Only {book.stock} left in stock</p>
          )}
          {book.stock === 0 && (
            <p className="text-red-500 text-sm mt-1">Out of stock</p>
          )}
        </CardContent>
        <CardFooter className="p-4 pt-0">
          <Button 
            onClick={handleAddToCart}
            disabled={book.stock === 0}
            className="w-full bg-elegant-gold hover:bg-elegant-gold/90 text-classic-black font-semibold"
          >
            {book.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
          </Button>
        </CardFooter>
      </Card>
    </Link>
  );
};

export default BookCard;
