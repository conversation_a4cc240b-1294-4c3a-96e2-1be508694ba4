
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Mail, Gift } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Welcome to Kotobcom! 🎉",
        description: "You've been subscribed to our newsletter. Enjoy 10% off your first order!",
      });
      setEmail('');
      setIsLoading(false);
    }, 1000);
  };

  return (
    <section className="py-16 bg-deep-red">
      <div className="container mx-auto px-4">
        <Card className="max-w-2xl mx-auto bg-clean-white">
          <CardContent className="p-8 text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-elegant-gold p-3 rounded-full">
                <Mail className="h-6 w-6 text-classic-black" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-classic-black mb-2">
              Stay Updated with Kotobcom
            </h3>
            <p className="text-gray-600 mb-6">
              Get the latest book recommendations, exclusive deals, and be the first to know about new arrivals!
            </p>
            
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1"
                required
              />
              <Button 
                type="submit" 
                disabled={isLoading}
                className="bg-elegant-gold hover:bg-elegant-gold/90 text-classic-black font-bold"
              >
                {isLoading ? 'Subscribing...' : 'Subscribe'}
              </Button>
            </form>
            
            <div className="flex items-center justify-center mt-4 text-sm text-gray-500">
              <Gift className="h-4 w-4 mr-1" />
              <span>Get 10% off your first order when you subscribe!</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NewsletterSignup;
