
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Mail, Gift, CheckCircle, Sparkles } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Welcome to Kotobcom! 🎉",
        description: "You've been subscribed to our newsletter. Enjoy 10% off your first order!",
      });
      setEmail('');
      setIsLoading(false);
      setIsSubscribed(true);

      // Reset success state after 3 seconds
      setTimeout(() => setIsSubscribed(false), 3000);
    }, 1000);
  };

  return (
    <section className="section-padding bg-gradient-to-br from-deep-red via-deep-red-700 to-deep-red-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
      </div>

      <div className="container mx-auto container-padding relative">
        <Card className="max-w-4xl mx-auto bg-clean-white/95 backdrop-blur-sm border-0 shadow-large overflow-hidden">
          <CardContent className="p-0">
            <div className="grid md:grid-cols-2 gap-0">
              {/* Left Side - Content */}
              <div className="p-8 md:p-12 flex flex-col justify-center">
                <div className="mb-6">
                  <Badge className="bg-elegant-gold/20 text-elegant-gold border-elegant-gold/30 mb-4">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Exclusive Access
                  </Badge>
                  <h3 className="text-3xl md:text-4xl font-bold text-classic-black mb-4">
                    Join Our Book Community
                  </h3>
                  <p className="text-warm-gray-600 text-lg leading-relaxed">
                    Get exclusive book recommendations, early access to new releases, and special member-only discounts delivered to your inbox.
                  </p>
                </div>

                {/* Benefits */}
                <div className="space-y-3 mb-8">
                  <div className="flex items-center text-warm-gray-700">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span>Weekly curated book recommendations</span>
                  </div>
                  <div className="flex items-center text-warm-gray-700">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span>Exclusive member discounts up to 25%</span>
                  </div>
                  <div className="flex items-center text-warm-gray-700">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span>Early access to new arrivals</span>
                  </div>
                  <div className="flex items-center text-warm-gray-700">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span>Author interviews and book insights</span>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1 h-12 border-warm-gray-200 focus:border-deep-red focus:ring-deep-red"
                      required
                      disabled={isLoading}
                    />
                    <Button
                      type="submit"
                      disabled={isLoading || isSubscribed}
                      className={`h-12 px-8 font-semibold transition-all duration-300 ${
                        isSubscribed
                          ? 'bg-green-500 hover:bg-green-600 text-clean-white'
                          : 'bg-elegant-gold hover:bg-elegant-gold-600 text-classic-black'
                      }`}
                    >
                      {isLoading ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-classic-black border-t-transparent mr-2"></div>
                          Subscribing...
                        </div>
                      ) : isSubscribed ? (
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Subscribed!
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2" />
                          Subscribe
                        </div>
                      )}
                    </Button>
                  </div>

                  <p className="text-xs text-warm-gray-500 text-center sm:text-left">
                    By subscribing, you agree to receive marketing emails. Unsubscribe at any time.
                  </p>
                </form>
              </div>

              {/* Right Side - Visual */}
              <div className="bg-gradient-to-br from-elegant-gold/20 to-deep-red/20 p-8 md:p-12 flex items-center justify-center">
                <div className="relative">
                  {/* Decorative Elements */}
                  <div className="absolute -top-4 -right-4 w-24 h-24 bg-elegant-gold/20 rounded-full blur-xl"></div>
                  <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-deep-red/10 rounded-full blur-xl"></div>

                  {/* Main Visual */}
                  <div className="relative bg-clean-white/50 backdrop-blur-sm rounded-2xl p-8 border border-clean-white/20">
                    <div className="text-center">
                      <div className="bg-gradient-to-br from-elegant-gold to-elegant-gold-600 p-6 rounded-2xl w-fit mx-auto mb-6">
                        <Mail className="h-12 w-12 text-classic-black" />
                      </div>
                      <h4 className="text-xl font-bold text-classic-black mb-2">
                        Join 5,000+ Readers
                      </h4>
                      <p className="text-warm-gray-600 text-sm">
                        Already discovering amazing books through our newsletter
                      </p>

                      {/* Trust Indicators */}
                      <div className="mt-6 flex items-center justify-center space-x-4 text-xs text-warm-gray-500">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span>No spam</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span>Unsubscribe anytime</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Special Offer Banner */}
        <div className="text-center mt-8">
          <Badge className="bg-elegant-gold text-classic-black px-6 py-3 text-lg font-bold animate-pulse">
            <Gift className="h-5 w-5 mr-2" />
            Limited Time: Get 10% off your first order when you subscribe!
          </Badge>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSignup;
