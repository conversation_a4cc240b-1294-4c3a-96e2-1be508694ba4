
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import BookCard from '@/components/ui/book-card';
import LoadingSkeleton from '@/components/ui/loading-skeleton';
import PromotionalBanner from '@/components/ui/promotional-banner';
import NewArrivals from '@/components/ui/new-arrivals';
import NewsletterSignup from '@/components/ui/newsletter-signup';
import { useBooks } from '@/context/BookContext';
import { BookOpen, Truck, Shield, Star, ArrowRight, Sparkles, Users, Award } from 'lucide-react';

const Home = () => {
  const { getFeaturedBooks, getAllCategories, loading } = useBooks();
  const featuredBooks = getFeaturedBooks();
  const categories = getAllCategories();

  return (
    <div className="bg-clean-white">
      {/* Promotional Banner */}
      <PromotionalBanner />

      {/* Enhanced Hero Section */}
      <section className="relative bg-gradient-to-br from-deep-red via-deep-red-700 to-deep-red-900 text-clean-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full bg-repeat" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
        </div>

        <div className="relative container mx-auto container-padding section-padding">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-center lg:text-left space-y-8">
              {/* Badge */}
              <div className="flex justify-center lg:justify-start">
                <Badge className="bg-elegant-gold/20 text-elegant-gold border-elegant-gold/30 px-4 py-2 text-sm font-medium animate-fade-in">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Tunisia's Premier Online Bookstore
                </Badge>
              </div>

              {/* Main Heading */}
              <div className="space-y-4">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight animate-fade-in-up">
                  Discover Your Next
                  <span className="block text-gradient">Great Read</span>
                </h1>
                <p className="text-xl md:text-2xl text-clean-white/90 max-w-2xl mx-auto lg:mx-0 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
                  From bestsellers to hidden gems, find the perfect book with convenient delivery and secure payment on arrival.
                </p>
              </div>

              {/* Stats */}
              <div className="flex flex-wrap justify-center lg:justify-start gap-8 text-center animate-fade-in-up" style={{animationDelay: '0.4s'}}>
                <div className="flex flex-col items-center">
                  <div className="text-2xl font-bold text-elegant-gold">1000+</div>
                  <div className="text-sm text-clean-white/70">Books Available</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-2xl font-bold text-elegant-gold">500+</div>
                  <div className="text-sm text-clean-white/70">Happy Customers</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-2xl font-bold text-elegant-gold">24h</div>
                  <div className="text-sm text-clean-white/70">Fast Delivery</div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-fade-in-up" style={{animationDelay: '0.6s'}}>
                <Link to="/books">
                  <Button size="lg" className="btn-secondary group w-full sm:w-auto">
                    <BookOpen className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform" />
                    Browse Books
                    <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                <Link to="/books?featured=true">
                  <Button size="lg" variant="outline" className="border-clean-white/30 text-clean-white hover:bg-clean-white/10 w-full sm:w-auto">
                    <Star className="h-5 w-5 mr-2" />
                    Featured Books
                  </Button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-clean-white/70 animate-fade-in-up" style={{animationDelay: '0.8s'}}>
                <div className="flex items-center">
                  <Shield className="h-4 w-4 mr-2 text-elegant-gold" />
                  Secure Payment
                </div>
                <div className="flex items-center">
                  <Truck className="h-4 w-4 mr-2 text-elegant-gold" />
                  Free Delivery $50+
                </div>
                <div className="flex items-center">
                  <Award className="h-4 w-4 mr-2 text-elegant-gold" />
                  Quality Guaranteed
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image/Illustration */}
            <div className="relative animate-fade-in-up" style={{animationDelay: '0.3s'}}>
              <div className="relative">
                {/* Decorative Elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-elegant-gold/20 rounded-full blur-xl"></div>
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-elegant-gold/10 rounded-full blur-xl"></div>

                {/* Main Hero Visual */}
                <div className="relative bg-gradient-to-br from-clean-white/10 to-clean-white/5 backdrop-blur-sm rounded-2xl p-8 border border-clean-white/20">
                  <div className="grid grid-cols-2 gap-4">
                    {/* Mock Book Covers */}
                    <div className="space-y-4">
                      <div className="bg-gradient-to-br from-elegant-gold to-elegant-gold-600 rounded-lg h-32 flex items-center justify-center shadow-large">
                        <BookOpen className="h-8 w-8 text-classic-black" />
                      </div>
                      <div className="bg-gradient-to-br from-sage-500 to-sage-700 rounded-lg h-24 flex items-center justify-center shadow-medium">
                        <BookOpen className="h-6 w-6 text-clean-white" />
                      </div>
                    </div>
                    <div className="space-y-4 mt-8">
                      <div className="bg-gradient-to-br from-warm-gray-600 to-warm-gray-800 rounded-lg h-24 flex items-center justify-center shadow-medium">
                        <BookOpen className="h-6 w-6 text-clean-white" />
                      </div>
                      <div className="bg-gradient-to-br from-deep-red-400 to-deep-red-600 rounded-lg h-32 flex items-center justify-center shadow-large">
                        <BookOpen className="h-8 w-8 text-clean-white" />
                      </div>
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute -top-2 left-4 bg-elegant-gold text-classic-black px-3 py-1 rounded-full text-xs font-bold">
                    NEW
                  </div>
                  <div className="absolute -bottom-2 right-4 bg-clean-white text-classic-black px-3 py-1 rounded-full text-xs font-bold flex items-center">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    4.9
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Wave */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg viewBox="0 0 1200 120" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-12 text-clean-white">
            <path d="M0 120L50 105C100 90 200 60 300 45C400 30 500 30 600 37.5C700 45 800 60 900 67.5C1000 75 1100 75 1150 75L1200 75V120H1150C1100 120 1000 120 900 120C800 120 700 120 600 120C500 120 400 120 300 120C200 120 100 120 50 120H0Z" fill="currentColor"/>
          </svg>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="section-padding bg-gradient-to-b from-clean-white to-warm-gray-50">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-deep-red/10 text-deep-red border-deep-red/20 mb-4">
              Why Choose Kotobcom
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black mb-4">
              Your Reading Journey Starts Here
            </h2>
            <p className="text-lg text-warm-gray-600 max-w-2xl mx-auto">
              We're committed to making your book shopping experience exceptional with our premium services and customer-first approach.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-elegant-gold to-elegant-gold-600 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <BookOpen className="h-8 w-8 text-classic-black" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black">Curated Collection</h3>
                <p className="text-warm-gray-600 leading-relaxed">
                  Handpicked books across fiction, non-fiction, academic, and specialty genres to satisfy every reader's taste.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-elegant-gold font-medium">
                  <Users className="h-4 w-4 mr-2" />
                  1000+ Titles Available
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-sage-500 to-sage-700 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Truck className="h-8 w-8 text-clean-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black">Lightning Fast Delivery</h3>
                <p className="text-warm-gray-600 leading-relaxed">
                  Same-day delivery in major cities, next-day nationwide. Track your order in real-time from our warehouse to your door.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-sage-600 font-medium">
                  <Star className="h-4 w-4 mr-2" />
                  24-48 Hour Delivery
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-deep-red to-deep-red-700 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-8 w-8 text-clean-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black">Secure & Convenient</h3>
                <p className="text-warm-gray-600 leading-relaxed">
                  Pay on delivery with cash or card. No upfront payment required. Inspect your books before paying.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-deep-red font-medium">
                  <Award className="h-4 w-4 mr-2" />
                  100% Secure Payment
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Trust Indicators */}
          <div className="mt-16 text-center">
            <div className="flex flex-wrap justify-center items-center gap-8 text-warm-gray-500">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">Free delivery on orders over $50</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">30-day return policy</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">Customer support 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* New Arrivals */}
      <NewArrivals />

      {/* Enhanced Categories Section */}
      <section className="section-padding bg-clean-white">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-sage-100 text-sage-700 border-sage-200 mb-4">
              Explore Genres
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-warm-gray-600 max-w-2xl mx-auto">
              Discover your next favorite book from our carefully curated categories spanning every genre and interest.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <LoadingSkeleton type="category-card" count={8} />
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {categories.map((category, index) => (
                <Link key={category} to={`/books?category=${category}`}>
                  <Card className="card-hover border-0 shadow-soft bg-clean-white group cursor-pointer overflow-hidden">
                    <CardContent className="p-8 text-center relative">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity">
                        <div className="w-full h-full bg-gradient-to-br from-deep-red to-elegant-gold"></div>
                      </div>

                      {/* Icon based on category */}
                      <div className="relative mb-4">
                        <div className={`w-12 h-12 mx-auto rounded-xl flex items-center justify-center text-clean-white font-bold text-lg ${
                          index % 4 === 0 ? 'bg-gradient-to-br from-deep-red to-deep-red-700' :
                          index % 4 === 1 ? 'bg-gradient-to-br from-elegant-gold to-elegant-gold-600' :
                          index % 4 === 2 ? 'bg-gradient-to-br from-sage-500 to-sage-700' :
                          'bg-gradient-to-br from-warm-gray-600 to-warm-gray-800'
                        } group-hover:scale-110 transition-transform duration-300`}>
                          {category.charAt(0)}
                        </div>
                      </div>

                      <h3 className="font-bold text-classic-black group-hover:text-deep-red transition-colors text-lg mb-2">
                        {category}
                      </h3>

                      {/* Mock book count */}
                      <p className="text-sm text-warm-gray-500">
                        {Math.floor(Math.random() * 50) + 10} books
                      </p>

                      {/* Hover Arrow */}
                      <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="h-4 w-4 text-deep-red" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}

          {/* View All Categories Button */}
          <div className="text-center mt-12">
            <Link to="/books">
              <Button variant="outline" className="border-deep-red text-deep-red hover:bg-deep-red hover:text-clean-white">
                View All Categories
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Enhanced Featured Books Section */}
      <section className="section-padding bg-gradient-to-b from-warm-gray-50 to-clean-white">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-deep-red/10 text-deep-red border-deep-red/20 mb-4">
              <Star className="h-3 w-3 mr-1" />
              Staff Picks
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black mb-4">
              Featured Books
            </h2>
            <p className="text-lg text-warm-gray-600 max-w-2xl mx-auto">
              Handpicked by our team of book lovers - these are the titles everyone's talking about.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <LoadingSkeleton type="book-card" count={6} />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {featuredBooks.map((book, index) => (
                  <div key={book.id} className="animate-fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                    <BookCard book={book} />
                  </div>
                ))}
              </div>

              {/* Call to Action */}
              <div className="text-center">
                <div className="bg-gradient-to-r from-deep-red to-deep-red-700 rounded-2xl p-8 md:p-12 text-clean-white">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    Discover More Amazing Books
                  </h3>
                  <p className="text-clean-white/90 mb-6 max-w-2xl mx-auto">
                    Explore our complete collection of over 1000 carefully selected titles across all genres and categories.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link to="/books">
                      <Button size="lg" className="btn-secondary">
                        <BookOpen className="h-5 w-5 mr-2" />
                        Browse All Books
                        <ArrowRight className="h-5 w-5 ml-2" />
                      </Button>
                    </Link>
                    <Link to="/books?featured=true">
                      <Button size="lg" variant="outline" className="border-clean-white/30 text-clean-white hover:bg-clean-white/10">
                        <Star className="h-5 w-5 mr-2" />
                        More Featured
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
};

export default Home;
