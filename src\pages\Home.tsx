
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import BookCard from '@/components/ui/book-card';
import PromotionalBanner from '@/components/ui/promotional-banner';
import NewArrivals from '@/components/ui/new-arrivals';
import NewsletterSignup from '@/components/ui/newsletter-signup';
import { useBooks } from '@/context/BookContext';
import { BookOpen, Truck, Shield } from 'lucide-react';

const Home = () => {
  const { getFeaturedBooks, getAllCategories, loading } = useBooks();
  const featuredBooks = getFeaturedBooks();
  const categories = getAllCategories();

  return (
    <div className="bg-clean-white">
      {/* Promotional Banner */}
      <PromotionalBanner />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-deep-red to-deep-red/90 text-clean-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6 animate-fade-in">
            Welcome to Kotobcom
          </h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Discover amazing books with convenient delivery right to your doorstep. 
            Payment on delivery - safe, simple, and secure.
          </p>
          <Link to="/books">
            <Button size="lg" className="bg-elegant-gold hover:bg-elegant-gold/90 text-classic-black font-bold px-8 py-3 text-lg">
              Shop Now
            </Button>
          </Link>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <BookOpen className="h-12 w-12 text-elegant-gold mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 text-classic-black">Wide Selection</h3>
                <p className="text-gray-600">Discover books across multiple genres and categories</p>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <Truck className="h-12 w-12 text-elegant-gold mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 text-classic-black">Fast Delivery</h3>
                <p className="text-gray-600">Quick and reliable delivery to your location</p>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <Shield className="h-12 w-12 text-elegant-gold mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 text-classic-black">Payment on Delivery</h3>
                <p className="text-gray-600">Pay safely when you receive your order</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* New Arrivals */}
      <NewArrivals />

      {/* Categories Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-classic-black">
            Browse by Category
          </h2>
          {loading ? (
            <div className="text-center">
              <p className="text-gray-600">Loading categories...</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {categories.map(category => (
                <Link key={category} to={`/books?category=${category}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardContent className="p-6 text-center">
                      <h3 className="font-semibold text-classic-black group-hover:text-deep-red transition-colors">
                        {category}
                      </h3>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Featured Books Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-classic-black">
            Featured Books
          </h2>
          {loading ? (
            <div className="text-center">
              <p className="text-gray-600">Loading featured books...</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {featuredBooks.map(book => (
                  <BookCard key={book.id} book={book} />
                ))}
              </div>
              <div className="text-center mt-8">
                <Link to="/books">
                  <Button className="bg-deep-red hover:bg-deep-red/90 text-clean-white">
                    View All Books
                  </Button>
                </Link>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
};

export default Home;
