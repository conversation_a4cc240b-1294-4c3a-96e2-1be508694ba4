
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import BookCard from '@/components/ui/book-card';
import LoadingSkeleton from '@/components/ui/loading-skeleton';

import PromotionalBanner from '@/components/ui/promotional-banner';
import NewArrivals from '@/components/ui/new-arrivals';
import NewsletterSignup from '@/components/ui/newsletter-signup';
import { useBooks } from '@/context/BookContext';
import { BookOpen, Truck, Shield, Star, ArrowRight, Sparkles, Users, Award } from 'lucide-react';

const Home = () => {
  const { getFeaturedBooks, getAllCategories, loading } = useBooks();
  const featuredBooks = getFeaturedBooks();
  const categories = getAllCategories();

  return (
    <div className="bg-background">

      {/* Redesigned Hero Section */}
      <section className="bg-deep-red dark:bg-deep-red-800 text-clean-white transition-colors duration-300 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-repeat opacity-20" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-12 lg:py-16 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-6 lg:pr-8">
              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-elegant-gold/20 dark:bg-accent/20 border border-elegant-gold/30 dark:border-accent/30 rounded-full text-elegant-gold dark:text-accent text-sm font-medium">
                <span className="mr-2">✨</span>
                Tunisia's Premier Online Bookstore
              </div>

              {/* Main Headline */}
              <div className="space-y-2">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Discover Your Next
                  <span className="block bg-gradient-to-r from-elegant-gold to-elegant-gold-400 dark:from-accent dark:to-accent/80 bg-clip-text text-transparent">
                    Great Read
                  </span>
                </h1>
              </div>

              {/* Sub-headline */}
              <p className="text-lg md:text-xl text-clean-white/90 leading-relaxed max-w-2xl">
                From bestsellers to hidden gems, find the perfect book with convenient delivery across Tunisia.
              </p>

              {/* Stats */}
              <div className="flex flex-wrap gap-6 py-2">
                <div className="text-center">
                  <div className="text-2xl font-bold text-elegant-gold dark:text-accent">1000+</div>
                  <div className="text-xs text-clean-white/70 dark:text-foreground/70">Books Available</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-elegant-gold dark:text-accent">500+</div>
                  <div className="text-xs text-clean-white/70 dark:text-foreground/70">Happy Customers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-elegant-gold dark:text-accent">24h</div>
                  <div className="text-xs text-clean-white/70 dark:text-foreground/70">Fast Delivery</div>
                </div>
              </div>

              {/* Call-to-Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-2">
                <Link to="/books">
                  <Button
                    className="bg-elegant-gold hover:bg-elegant-gold-600 dark:bg-accent dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground font-bold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                  >
                    Browse All Books
                    <span className="ml-2">→</span>
                  </Button>
                </Link>
                <Link to="/books?featured=true">
                  <Button
                    variant="outline"
                    className="border-2 border-clean-white/30 dark:border-foreground/30 text-clean-white dark:text-foreground hover:bg-clean-white/10 dark:hover:bg-foreground/10 hover:border-elegant-gold dark:hover:border-accent font-bold px-6 py-3 rounded-lg transition-all duration-300 w-full sm:w-auto"
                  >
                    Featured Books
                  </Button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap gap-4 text-sm text-clean-white/70 pt-2">
                <div className="flex items-center">
                  <span className="mr-2">🛡️</span>
                  Secure Payment
                </div>
                <div className="flex items-center">
                  <span className="mr-2">🚚</span>
                  Free Delivery 50+ TND
                </div>
                <div className="flex items-center">
                  <span className="mr-2">⭐</span>
                  Quality Guaranteed
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image */}
            <div className="relative lg:pl-4 hero-float flex justify-center lg:justify-end">
              {/* Main Image Container */}
              <div className="relative hero-image-container">
                {/* Background Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-elegant-gold/40 to-deep-red/20 rounded-2xl blur-2xl transform rotate-2 scale-105 hero-glow"></div>

                {/* Image Container */}
                <div className="relative aspect-square w-full max-w-lg rounded-2xl overflow-hidden shadow-2xl border-4 border-elegant-gold/30 bg-gradient-to-br from-elegant-gold/10 to-deep-red/20 transform transition-all duration-500 hover:shadow-3xl hover:border-elegant-gold/50">
                  <img
                    src="/kotobcom.png"
                    alt="Kotobcom - Your trusted bookstore for discovering amazing books"
                    className="w-full h-full object-contain hero-image p-4"
                    onError={(e) => {
                      // Fallback to a high-quality bookstore image
                      const target = e.target as HTMLImageElement;
                      target.src = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80";
                      target.alt = "Cozy reading corner with vintage books - Kotobcom bookstore";
                      target.className = "w-full h-full object-cover hero-image";
                    }}
                  />

                  {/* Image Overlay for Better Integration */}
                  <div className="absolute inset-0 bg-gradient-to-t from-deep-red/20 via-transparent to-transparent"></div>

                  {/* Floating Badge */}
                  <div className="absolute top-6 left-6 bg-elegant-gold text-classic-black px-5 py-3 rounded-full text-sm font-bold shadow-xl transform hover:scale-105 transition-transform duration-300">
                    📚 Premium Books
                  </div>

                  {/* Bottom Stats Badge */}
                  <div className="absolute bottom-6 right-6 bg-clean-white/95 backdrop-blur-sm text-classic-black px-5 py-3 rounded-full text-sm font-bold shadow-xl flex items-center space-x-2 transform hover:scale-105 transition-transform duration-300">
                    <span className="text-elegant-gold text-lg">⭐</span>
                    <span>1000+ Books</span>
                  </div>
                </div>
              </div>

              {/* Enhanced Decorative Elements */}
              <div className="absolute -top-8 -right-8 w-24 h-24 bg-elegant-gold/20 rounded-full blur-2xl hero-glow"></div>
              <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-elegant-gold/10 rounded-full blur-2xl hero-glow" style={{animationDelay: '2s'}}></div>

              {/* Floating Book Icons */}
              <div className="absolute top-1/4 -left-4 w-8 h-8 bg-elegant-gold/40 rounded-lg rotate-12 hero-float shadow-md" style={{animationDelay: '1s'}}></div>
              <div className="absolute bottom-1/3 -right-6 w-6 h-6 bg-elegant-gold/50 rounded-lg -rotate-12 hero-float shadow-md" style={{animationDelay: '3s'}}></div>
            </div>
          </div>
        </div>
      </section>

      {/* Original Promotional Banner */}
      <PromotionalBanner />

      {/* Enhanced Features Section */}
      <section className="section-padding bg-gradient-to-b from-clean-white to-warm-gray-50 dark:from-background dark:to-card">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-deep-red/10 dark:bg-primary/20 text-deep-red dark:text-primary border-deep-red/20 dark:border-primary/30 mb-4">
              Why Choose Kotobcom
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black dark:text-foreground mb-4">
              Your Reading Journey Starts Here
            </h2>
            <p className="text-lg text-warm-gray-600 dark:text-muted-foreground max-w-2xl mx-auto">
              We're committed to making your book shopping experience exceptional with our premium services and customer-first approach.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white dark:bg-card group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-elegant-gold to-elegant-gold-600 dark:from-accent dark:to-accent/80 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <BookOpen className="h-8 w-8 text-classic-black dark:text-accent-foreground" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black dark:text-card-foreground">Curated Collection</h3>
                <p className="text-warm-gray-600 dark:text-muted-foreground leading-relaxed">
                  Handpicked books across fiction, non-fiction, academic, and specialty genres to satisfy every reader's taste.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-elegant-gold dark:text-accent font-medium">
                  <Users className="h-4 w-4 mr-2" />
                  1000+ Titles Available
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white dark:bg-card group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-sage-500 to-sage-700 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Truck className="h-8 w-8 text-clean-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black dark:text-card-foreground">Lightning Fast Delivery</h3>
                <p className="text-warm-gray-600 dark:text-muted-foreground leading-relaxed">
                  Same-day delivery in major cities, next-day nationwide. Track your order in real-time from our warehouse to your door.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-sage-600 dark:text-sage-400 font-medium">
                  <Star className="h-4 w-4 mr-2" />
                  24-48 Hour Delivery
                </div>
              </CardContent>
            </Card>

            <Card className="card-hover text-center p-8 border-0 shadow-soft bg-clean-white dark:bg-card group">
              <CardContent className="pt-6">
                <div className="bg-gradient-to-br from-deep-red to-deep-red-700 dark:from-primary dark:to-primary/80 p-4 rounded-2xl w-fit mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-8 w-8 text-clean-white dark:text-primary-foreground" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-classic-black dark:text-card-foreground">Secure & Convenient</h3>
                <p className="text-warm-gray-600 dark:text-muted-foreground leading-relaxed">
                  Pay on delivery with cash or card. No upfront payment required. Inspect your books before paying.
                </p>
                <div className="mt-4 flex items-center justify-center text-sm text-deep-red dark:text-primary font-medium">
                  <Award className="h-4 w-4 mr-2" />
                  100% Secure Payment
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Trust Indicators */}
          <div className="mt-16 text-center">
            <div className="flex flex-wrap justify-center items-center gap-8 text-warm-gray-500">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">Free delivery on orders over $50</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">30-day return policy</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold rounded-full mr-3"></div>
                <span className="text-sm">Customer support 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* New Arrivals */}
      <NewArrivals />

      {/* Enhanced Categories Section */}
      <section className="section-padding bg-clean-white dark:bg-background">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-sage-100 dark:bg-sage-900/30 text-sage-700 dark:text-sage-300 border-sage-200 dark:border-sage-700 mb-4">
              Explore Genres
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black dark:text-foreground mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-warm-gray-600 dark:text-muted-foreground max-w-2xl mx-auto">
              Discover your next favorite book from our carefully curated categories spanning every genre and interest.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <LoadingSkeleton type="category-card" count={8} />
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {categories.map((category, index) => (
                <Link key={category} to={`/books?category=${category}`}>
                  <Card className="card-hover border-0 shadow-soft bg-clean-white dark:bg-card group cursor-pointer overflow-hidden">
                    <CardContent className="p-8 text-center relative">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity">
                        <div className="w-full h-full bg-gradient-to-br from-deep-red to-elegant-gold dark:from-primary dark:to-accent"></div>
                      </div>

                      {/* Icon based on category */}
                      <div className="relative mb-4">
                        <div className={`w-12 h-12 mx-auto rounded-xl flex items-center justify-center text-clean-white font-bold text-lg ${
                          index % 4 === 0 ? 'bg-gradient-to-br from-deep-red to-deep-red-700 dark:from-primary dark:to-primary/80' :
                          index % 4 === 1 ? 'bg-gradient-to-br from-elegant-gold to-elegant-gold-600 dark:from-accent dark:to-accent/80' :
                          index % 4 === 2 ? 'bg-gradient-to-br from-sage-500 to-sage-700' :
                          'bg-gradient-to-br from-warm-gray-600 to-warm-gray-800'
                        } group-hover:scale-110 transition-transform duration-300`}>
                          {category.charAt(0)}
                        </div>
                      </div>

                      <h3 className="font-bold text-classic-black dark:text-card-foreground group-hover:text-deep-red dark:group-hover:text-primary transition-colors text-lg mb-2">
                        {category}
                      </h3>

                      {/* Mock book count */}
                      <p className="text-sm text-warm-gray-500 dark:text-muted-foreground">
                        {Math.floor(Math.random() * 50) + 10} books
                      </p>

                      {/* Hover Arrow */}
                      <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="h-4 w-4 text-deep-red dark:text-primary" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}

          {/* View All Categories Button */}
          <div className="text-center mt-12">
            <Link to="/books">
              <Button variant="outline" className="border-deep-red dark:border-primary text-deep-red dark:text-primary hover:bg-deep-red dark:hover:bg-primary hover:text-clean-white dark:hover:text-primary-foreground">
                View All Categories
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Enhanced Featured Books Section */}
      <section className="section-padding bg-gradient-to-b from-warm-gray-50 to-clean-white dark:from-card dark:to-background">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-deep-red/10 dark:bg-primary/20 text-deep-red dark:text-primary border-deep-red/20 dark:border-primary/30 mb-4">
              <Star className="h-3 w-3 mr-1" />
              Staff Picks
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black dark:text-foreground mb-4">
              Featured Books
            </h2>
            <p className="text-lg text-warm-gray-600 dark:text-muted-foreground max-w-2xl mx-auto">
              Handpicked by our team of book lovers - these are the titles everyone's talking about.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <LoadingSkeleton type="book-card" count={6} />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {featuredBooks.map((book, index) => (
                  <div key={book.id} className="animate-fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                    <BookCard book={book} />
                  </div>
                ))}
              </div>

              {/* Call to Action */}
              <div className="text-center">
                <div className="bg-gradient-to-r from-deep-red to-deep-red-700 rounded-2xl p-8 md:p-12 text-clean-white">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    Discover More Amazing Books
                  </h3>
                  <p className="text-clean-white/90 mb-6 max-w-2xl mx-auto">
                    Explore our complete collection of over 1000 carefully selected titles across all genres and categories.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link to="/books">
                      <Button size="lg" className="btn-secondary">
                        <BookOpen className="h-5 w-5 mr-2" />
                        Browse All Books
                        <ArrowRight className="h-5 w-5 ml-2" />
                      </Button>
                    </Link>
                    <Link to="/books?featured=true">
                      <Button size="lg" variant="outline" className="border-clean-white/30 text-clean-white hover:bg-clean-white/10">
                        <Star className="h-5 w-5 mr-2" />
                        More Featured
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
};

export default Home;
