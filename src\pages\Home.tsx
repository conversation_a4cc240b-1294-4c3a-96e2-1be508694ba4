
import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import BookCard from '@/components/ui/book-card';
import LoadingSkeleton from '@/components/ui/loading-skeleton';

import PromotionalBanner from '@/components/ui/promotional-banner';
import NewArrivals from '@/components/ui/new-arrivals';
import NewsletterSignup from '@/components/ui/newsletter-signup';
import { useBooks } from '@/context/BookContext';
import { BookOpen, Truck, Shield, Star, ArrowRight, Sparkles, Users, Award } from 'lucide-react';

const Home = () => {
  const { getFeaturedBooks, getAllCategories, loading } = useBooks();
  const featuredBooks = getFeaturedBooks();
  const categories = getAllCategories();

  return (
    <div className="bg-background">

      {/* Modern Hero Section */}
      <section className="gradient-primary text-primary-foreground relative overflow-hidden min-h-screen flex items-center">
        {/* Modern Background Effects */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-96 h-96 bg-accent/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary/30 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent/20 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1s'}}></div>
        </div>

        <div className="container mx-auto px-4 py-20 lg:py-32 relative">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-8 lg:pr-8">
              {/* Modern Badge */}
              <div className="inline-flex items-center px-6 py-3 bg-accent/20 border border-accent/30 rounded-full text-accent text-sm font-semibold backdrop-blur-sm">
                <span className="mr-2 text-lg">✨</span>
                Tunisia's Premier Online Bookstore
              </div>

              {/* Modern Headline */}
              <div className="space-y-4">
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight">
                  Discover Your Next
                  <span className="block bg-gradient-to-r from-accent to-accent/80 bg-clip-text text-transparent">
                    Great Read
                  </span>
                </h1>
              </div>

              {/* Modern Sub-headline */}
              <p className="text-xl md:text-2xl text-primary-foreground/90 leading-relaxed max-w-2xl font-light">
                From bestsellers to hidden gems, find the perfect book with convenient delivery across Tunisia.
              </p>

              {/* Modern Stats Grid */}
              <div className="grid grid-cols-3 gap-6 py-4">
                <div className="text-center bg-primary-foreground/10 backdrop-blur-sm rounded-2xl p-6">
                  <div className="text-3xl font-bold text-accent glow-accent">1000+</div>
                  <div className="text-sm text-primary-foreground/80 font-medium">Books Available</div>
                </div>
                <div className="text-center bg-primary-foreground/10 backdrop-blur-sm rounded-2xl p-6">
                  <div className="text-3xl font-bold text-accent glow-accent">500+</div>
                  <div className="text-sm text-primary-foreground/80 font-medium">Happy Customers</div>
                </div>
                <div className="text-center bg-primary-foreground/10 backdrop-blur-sm rounded-2xl p-6">
                  <div className="text-3xl font-bold text-accent glow-accent">24h</div>
                  <div className="text-sm text-primary-foreground/80 font-medium">Fast Delivery</div>
                </div>
              </div>

              {/* Modern Call-to-Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 pt-4">
                <Link to="/books">
                  <Button className="btn-secondary w-full sm:w-auto text-lg px-8 py-4">
                    Browse All Books
                    <span className="ml-2">→</span>
                  </Button>
                </Link>
                <Link to="/books?featured=true">
                  <Button className="btn-outline w-full sm:w-auto text-lg px-8 py-4 border-primary-foreground/30 text-primary-foreground hover:bg-primary-foreground/10">
                    Featured Books
                  </Button>
                </Link>
              </div>

              {/* Modern Trust Indicators */}
              <div className="flex flex-wrap gap-6 text-base text-primary-foreground/80 pt-4">
                <div className="flex items-center bg-primary-foreground/5 backdrop-blur-sm rounded-xl px-4 py-2">
                  <span className="mr-3 text-xl">🛡️</span>
                  <span className="font-medium">Secure Payment</span>
                </div>
                <div className="flex items-center bg-primary-foreground/5 backdrop-blur-sm rounded-xl px-4 py-2">
                  <span className="mr-3 text-xl">🚚</span>
                  <span className="font-medium">Free Delivery 50+ TND</span>
                </div>
                <div className="flex items-center bg-primary-foreground/5 backdrop-blur-sm rounded-xl px-4 py-2">
                  <span className="mr-3 text-xl">⭐</span>
                  <span className="font-medium">Quality Guaranteed</span>
                </div>
              </div>
            </div>

            {/* Right Content - Hero Image */}
            <div className="relative lg:pl-4 hero-float flex justify-center lg:justify-end">
              {/* Main Image Container */}
              <div className="relative hero-image-container">
                {/* Background Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-elegant-gold/40 to-deep-red/20 rounded-2xl blur-2xl transform rotate-2 scale-105 hero-glow"></div>

                {/* Image Container */}
                <div className="relative aspect-square w-full max-w-lg rounded-2xl overflow-hidden shadow-2xl border-4 border-elegant-gold/30 bg-gradient-to-br from-elegant-gold/10 to-deep-red/20 transform transition-all duration-500 hover:shadow-3xl hover:border-elegant-gold/50">
                  <img
                    src="/kotobcom.png"
                    alt="Kotobcom - Your trusted bookstore for discovering amazing books"
                    className="w-full h-full object-contain hero-image p-4"
                    onError={(e) => {
                      // Fallback to a high-quality bookstore image
                      const target = e.target as HTMLImageElement;
                      target.src = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80";
                      target.alt = "Cozy reading corner with vintage books - Kotobcom bookstore";
                      target.className = "w-full h-full object-cover hero-image";
                    }}
                  />

                  {/* Image Overlay for Better Integration */}
                  <div className="absolute inset-0 bg-gradient-to-t from-deep-red/20 via-transparent to-transparent"></div>

                  {/* Floating Badge */}
                  <div className="absolute top-6 left-6 bg-elegant-gold text-classic-black px-5 py-3 rounded-full text-sm font-bold shadow-xl transform hover:scale-105 transition-transform duration-300">
                    📚 Premium Books
                  </div>

                  {/* Bottom Stats Badge */}
                  <div className="absolute bottom-6 right-6 bg-clean-white/95 backdrop-blur-sm text-classic-black px-5 py-3 rounded-full text-sm font-bold shadow-xl flex items-center space-x-2 transform hover:scale-105 transition-transform duration-300">
                    <span className="text-elegant-gold text-lg">⭐</span>
                    <span>1000+ Books</span>
                  </div>
                </div>
              </div>

              {/* Enhanced Decorative Elements */}
              <div className="absolute -top-8 -right-8 w-24 h-24 bg-elegant-gold/20 rounded-full blur-2xl hero-glow"></div>
              <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-elegant-gold/10 rounded-full blur-2xl hero-glow" style={{animationDelay: '2s'}}></div>

              {/* Floating Book Icons */}
              <div className="absolute top-1/4 -left-4 w-8 h-8 bg-elegant-gold/40 rounded-lg rotate-12 hero-float shadow-md" style={{animationDelay: '1s'}}></div>
              <div className="absolute bottom-1/3 -right-6 w-6 h-6 bg-elegant-gold/50 rounded-lg -rotate-12 hero-float shadow-md" style={{animationDelay: '3s'}}></div>
            </div>
          </div>
        </div>
      </section>

      {/* Original Promotional Banner */}
      <PromotionalBanner />

      {/* Modern Features Section */}
      <section className="section-padding gradient-surface">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-20">
            <div className="badge-modern mb-6">
              Why Choose Kotobcom
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Your Reading Journey Starts Here
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              We're committed to making your book shopping experience exceptional with our premium services and customer-first approach.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            <Card className="card-modern text-center p-10 group glow-accent">
              <CardContent className="pt-8">
                <div className="gradient-accent p-6 rounded-3xl w-fit mx-auto mb-8 group-hover:scale-110 transition-transform duration-300 glow-accent">
                  <BookOpen className="h-10 w-10 text-accent-foreground" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-card-foreground">Curated Collection</h3>
                <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                  Handpicked books across fiction, non-fiction, academic, and specialty genres to satisfy every reader's taste.
                </p>
                <div className="badge-success">
                  <Users className="h-4 w-4 mr-2" />
                  1000+ Titles Available
                </div>
              </CardContent>
            </Card>

            <Card className="card-modern text-center p-10 group glow-primary">
              <CardContent className="pt-8">
                <div className="gradient-primary p-6 rounded-3xl w-fit mx-auto mb-8 group-hover:scale-110 transition-transform duration-300 glow-primary">
                  <Truck className="h-10 w-10 text-primary-foreground" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-card-foreground">Lightning Fast Delivery</h3>
                <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                  Same-day delivery in major cities, next-day nationwide. Track your order in real-time from our warehouse to your door.
                </p>
                <div className="badge-warning">
                  <Star className="h-4 w-4 mr-2" />
                  24-48 Hour Delivery
                </div>
              </CardContent>
            </Card>

            <Card className="card-modern text-center p-10 group glow-success">
              <CardContent className="pt-8">
                <div className="bg-gradient-to-br from-success to-success/80 p-6 rounded-3xl w-fit mx-auto mb-8 group-hover:scale-110 transition-transform duration-300 glow-success">
                  <Shield className="h-10 w-10 text-success-foreground" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-card-foreground">Secure & Convenient</h3>
                <p className="text-muted-foreground leading-relaxed text-lg mb-6">
                  Pay on delivery with cash or card. No upfront payment required. Inspect your books before paying.
                </p>
                <div className="badge-success">
                  <Award className="h-4 w-4 mr-2" />
                  100% Secure Payment
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Trust Indicators */}
          <div className="mt-16 text-center">
            <div className="flex flex-wrap justify-center items-center gap-8 text-warm-gray-500 dark:text-muted-foreground">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold dark:bg-accent rounded-full mr-3"></div>
                <span className="text-sm">Free delivery on orders over $50</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold dark:bg-accent rounded-full mr-3"></div>
                <span className="text-sm">30-day return policy</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-elegant-gold dark:bg-accent rounded-full mr-3"></div>
                <span className="text-sm">Customer support 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* New Arrivals */}
      <NewArrivals />

      {/* Enhanced Categories Section */}
      <section className="section-padding bg-background dark:bg-background">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-sage-100 dark:bg-sage-900/30 text-sage-700 dark:text-sage-300 border-sage-200 dark:border-sage-700 mb-4">
              Explore Genres
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black dark:text-foreground mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-warm-gray-600 dark:text-muted-foreground max-w-2xl mx-auto">
              Discover your next favorite book from our carefully curated categories spanning every genre and interest.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <LoadingSkeleton type="category-card" count={8} />
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {categories.map((category, index) => (
                <Link key={category} to={`/books?category=${category}`}>
                  <Card className="card-hover border-0 shadow-soft bg-card dark:bg-card group cursor-pointer overflow-hidden">
                    <CardContent className="p-8 text-center relative">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity">
                        <div className="w-full h-full bg-gradient-to-br from-deep-red to-elegant-gold dark:from-primary dark:to-accent"></div>
                      </div>

                      {/* Icon based on category */}
                      <div className="relative mb-4">
                        <div className={`w-12 h-12 mx-auto rounded-xl flex items-center justify-center text-clean-white font-bold text-lg ${
                          index % 4 === 0 ? 'bg-gradient-to-br from-deep-red to-deep-red-700 dark:from-primary dark:to-primary/80' :
                          index % 4 === 1 ? 'bg-gradient-to-br from-elegant-gold to-elegant-gold-600 dark:from-accent dark:to-accent/80' :
                          index % 4 === 2 ? 'bg-gradient-to-br from-sage-500 to-sage-700' :
                          'bg-gradient-to-br from-warm-gray-600 to-warm-gray-800'
                        } group-hover:scale-110 transition-transform duration-300`}>
                          {category.charAt(0)}
                        </div>
                      </div>

                      <h3 className="font-bold text-classic-black dark:text-card-foreground group-hover:text-deep-red dark:group-hover:text-primary transition-colors text-lg mb-2">
                        {category}
                      </h3>

                      {/* Mock book count */}
                      <p className="text-sm text-warm-gray-500 dark:text-muted-foreground">
                        {Math.floor(Math.random() * 50) + 10} books
                      </p>

                      {/* Hover Arrow */}
                      <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                        <ArrowRight className="h-4 w-4 text-deep-red dark:text-primary" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}

          {/* View All Categories Button */}
          <div className="text-center mt-12">
            <Link to="/books">
              <Button variant="outline" className="border-deep-red dark:border-primary text-deep-red dark:text-primary hover:bg-deep-red dark:hover:bg-primary hover:text-clean-white dark:hover:text-primary-foreground">
                View All Categories
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Enhanced Featured Books Section */}
      <section className="section-padding bg-gradient-to-b from-muted/30 to-background dark:from-card dark:to-background">
        <div className="container mx-auto container-padding">
          <div className="text-center mb-16">
            <Badge className="bg-deep-red/10 dark:bg-primary/20 text-deep-red dark:text-primary border-deep-red/20 dark:border-primary/30 mb-4">
              <Star className="h-3 w-3 mr-1" />
              Staff Picks
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-classic-black dark:text-foreground mb-4">
              Featured Books
            </h2>
            <p className="text-lg text-warm-gray-600 dark:text-muted-foreground max-w-2xl mx-auto">
              Handpicked by our team of book lovers - these are the titles everyone's talking about.
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <LoadingSkeleton type="book-card" count={6} />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {featuredBooks.map((book, index) => (
                  <div key={book.id} className="animate-fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                    <BookCard book={book} />
                  </div>
                ))}
              </div>

              {/* Call to Action */}
              <div className="text-center">
                <div className="bg-gradient-to-r from-deep-red to-deep-red-700 rounded-2xl p-8 md:p-12 text-clean-white">
                  <h3 className="text-2xl md:text-3xl font-bold mb-4">
                    Discover More Amazing Books
                  </h3>
                  <p className="text-clean-white/90 mb-6 max-w-2xl mx-auto">
                    Explore our complete collection of over 1000 carefully selected titles across all genres and categories.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link to="/books">
                      <Button size="lg" className="btn-secondary">
                        <BookOpen className="h-5 w-5 mr-2" />
                        Browse All Books
                        <ArrowRight className="h-5 w-5 ml-2" />
                      </Button>
                    </Link>
                    <Link to="/books?featured=true">
                      <Button size="lg" variant="outline" className="border-clean-white/30 text-clean-white hover:bg-clean-white/10">
                        <Star className="h-5 w-5 mr-2" />
                        More Featured
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
};

export default Home;
