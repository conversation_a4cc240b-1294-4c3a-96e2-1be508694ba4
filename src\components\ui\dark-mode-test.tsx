import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useTheme } from '@/context/ThemeContext';
import { 
  Moon, 
  Sun, 
  BookOpen, 
  Star, 
  Heart, 
  ShoppingCart,
  CheckCircle,
  AlertCircle,
  Info,
  Zap
} from 'lucide-react';

/**
 * Dark Mode Test Component
 * 
 * This component provides a comprehensive test suite for the dark mode implementation.
 * It displays various UI elements to verify that all components properly adapt to theme changes.
 * 
 * Usage: Add this component to any page during development to test dark mode functionality.
 */
const DarkModeTest: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="p-8 space-y-8 bg-background text-foreground transition-colors duration-300">
      <div className="text-center">
        <div className="badge-modern mb-6 animate-bounce-in">Theme Testing Suite</div>
        <h1 className="text-5xl md:text-6xl font-bold mb-6 text-gradient-modern animate-slide-up">Modern Theme System</h1>
        <p className="text-muted-foreground mb-8 text-xl max-w-3xl mx-auto animate-slide-up">
          Comprehensive testing for the redesigned color palette and enhanced UI/UX across both light and dark modes
        </p>
        <Button onClick={toggleTheme} className="btn-secondary text-xl px-8 py-4 mb-12 animate-bounce-in hover-glow">
          {theme === 'light' ? <Moon className="mr-3 h-6 w-6" /> : <Sun className="mr-3 h-6 w-6" />}
          Switch to {theme === 'light' ? 'Modern Dark' : 'Modern Light'} Mode
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Modern Color Palette Test */}
        <Card className="card-modern glow-accent hover-lift">
          <CardHeader>
            <CardTitle className="text-xl font-bold">Modern Color Palette</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-4 bg-background border rounded-xl font-medium">Background</div>
              <div className="p-4 bg-card border rounded-xl font-medium">Card Background</div>
              <div className="p-4 bg-primary text-primary-foreground rounded-xl font-medium glow-primary">Primary (Modern Crimson)</div>
              <div className="p-4 bg-accent text-accent-foreground rounded-xl font-medium glow-accent">Accent (Vibrant Gold)</div>
              <div className="p-4 bg-success text-success-foreground rounded-xl font-medium glow-success">Success (Fresh Green)</div>
              <div className="p-4 bg-warning text-warning-foreground rounded-xl font-medium">Warning (Vibrant Orange)</div>
              <div className="p-4 bg-muted text-muted-foreground rounded-xl font-medium">Muted</div>
            </div>
          </CardContent>
        </Card>

        {/* Typography Test */}
        <Card>
          <CardHeader>
            <CardTitle>Typography</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <h1 className="text-2xl font-bold">Heading 1</h1>
            <h2 className="text-xl font-semibold">Heading 2</h2>
            <h3 className="text-lg font-medium">Heading 3</h3>
            <p className="text-foreground">Regular paragraph text</p>
            <p className="text-muted-foreground">Muted text</p>
            <p className="text-sm text-muted-foreground">Small muted text</p>
          </CardContent>
        </Card>

        {/* Modern Button Variants Test */}
        <Card className="card-modern glow-primary hover-lift">
          <CardHeader>
            <CardTitle className="text-xl font-bold">Modern Button System</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full">Primary Button</Button>
            <Button variant="secondary" className="w-full">Secondary Button</Button>
            <Button variant="outline" className="w-full">Outline Button</Button>
            <Button variant="success" className="w-full">Success Button</Button>
            <Button variant="warning" className="w-full">Warning Button</Button>
            <Button variant="destructive" className="w-full">Destructive Button</Button>
            <Button variant="ghost" className="w-full">Ghost Button</Button>
          </CardContent>
        </Card>

        {/* Badge Test */}
        <Card>
          <CardHeader>
            <CardTitle>Badges & Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex flex-wrap gap-2">
              <Badge>Default Badge</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Success state</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span>Error state</span>
              </div>
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-500" />
                <span>Info state</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Elements Test */}
        <Card>
          <CardHeader>
            <CardTitle>Form Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Input placeholder="Text input field" />
            <Input type="email" placeholder="Email input" />
            <Input type="password" placeholder="Password input" />
            <div className="flex gap-2">
              <Input placeholder="Search..." className="flex-1" />
              <Button size="icon">
                <BookOpen className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Icons Test */}
        <Card>
          <CardHeader>
            <CardTitle>Icons & Graphics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div className="space-y-2">
                <BookOpen className="h-8 w-8 mx-auto text-primary" />
                <span className="text-xs">Primary</span>
              </div>
              <div className="space-y-2">
                <Star className="h-8 w-8 mx-auto text-accent" />
                <span className="text-xs">Accent</span>
              </div>
              <div className="space-y-2">
                <Heart className="h-8 w-8 mx-auto text-red-500" />
                <span className="text-xs">Red</span>
              </div>
              <div className="space-y-2">
                <ShoppingCart className="h-8 w-8 mx-auto text-muted-foreground" />
                <span className="text-xs">Muted</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shadows & Elevation Test */}
        <Card>
          <CardHeader>
            <CardTitle>Shadows & Elevation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-card rounded shadow-soft">Soft Shadow</div>
            <div className="p-4 bg-card rounded shadow-medium">Medium Shadow</div>
            <div className="p-4 bg-card rounded shadow-large">Large Shadow</div>
          </CardContent>
        </Card>

        {/* Interactive Elements Test */}
        <Card>
          <CardHeader>
            <CardTitle>Interactive Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <button className="w-full p-3 bg-card hover:bg-card/80 border rounded transition-colors">
              Hover Effect Card
            </button>
            <button className="w-full p-3 bg-accent hover:bg-accent/90 text-accent-foreground rounded transition-colors">
              Accent Hover Button
            </button>
            <div className="p-3 border rounded cursor-pointer hover:border-accent transition-colors">
              Hover Border Effect
            </div>
          </CardContent>
        </Card>

        {/* Gradient Test */}
        <Card>
          <CardHeader>
            <CardTitle>Gradients & Effects</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-4 bg-gradient-to-r from-primary to-accent text-primary-foreground rounded">
              Brand Gradient
            </div>
            <div className="p-4 bg-gradient-to-br from-background to-card border rounded">
              Subtle Gradient
            </div>
            <div className="p-4 bg-gradient-to-r from-accent/20 to-primary/20 rounded">
              Accent Overlay
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Theme Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Theme Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Current Theme</h4>
              <p className="text-muted-foreground">
                Active theme: <span className="font-mono bg-muted px-2 py-1 rounded">{theme}</span>
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Color Values</h4>
              <div className="space-y-1 text-sm">
                <div>Background: <span className="font-mono">hsl(var(--background))</span></div>
                <div>Foreground: <span className="font-mono">hsl(var(--foreground))</span></div>
                <div>Primary: <span className="font-mono">hsl(var(--primary))</span></div>
                <div>Accent: <span className="font-mono">hsl(var(--accent))</span></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center text-muted-foreground">
        <div className="bg-card rounded-2xl p-8 max-w-2xl mx-auto glow-success">
          <p className="text-2xl font-bold text-success mb-4">✅ Modern Theme System Complete</p>
          <p className="text-lg mb-3">
            All components feature the new sophisticated color palette with enhanced visual effects
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="badge-success">Modern Light Mode</div>
            <div className="badge-modern">Sophisticated Dark Mode</div>
            <div className="badge-warning">Enhanced Animations</div>
            <div className="badge-modern">Premium UI/UX</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DarkModeTest;
