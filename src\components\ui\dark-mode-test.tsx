import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useTheme } from '@/context/ThemeContext';
import { 
  Moon, 
  Sun, 
  BookOpen, 
  Star, 
  Heart, 
  ShoppingCart,
  CheckCircle,
  AlertCircle,
  Info,
  Zap
} from 'lucide-react';

/**
 * Dark Mode Test Component
 * 
 * This component provides a comprehensive test suite for the dark mode implementation.
 * It displays various UI elements to verify that all components properly adapt to theme changes.
 * 
 * Usage: Add this component to any page during development to test dark mode functionality.
 */
const DarkModeTest: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="p-8 space-y-8 bg-background text-foreground transition-colors duration-300">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Theme Test Suite</h1>
        <p className="text-muted-foreground mb-6">
          Comprehensive testing for the enhanced light and dark mode implementation
        </p>
        <Button onClick={toggleTheme} className="mb-8">
          {theme === 'light' ? <Moon className="mr-2 h-4 w-4" /> : <Sun className="mr-2 h-4 w-4" />}
          Switch to {theme === 'light' ? 'Enhanced Dark' : 'Enhanced Light'} Mode
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Color Palette Test */}
        <Card>
          <CardHeader>
            <CardTitle>Color Palette</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="p-3 bg-background border rounded">Background</div>
              <div className="p-3 bg-card border rounded">Card Background</div>
              <div className="p-3 bg-primary text-primary-foreground rounded">Primary (Deep Red)</div>
              <div className="p-3 bg-accent text-accent-foreground rounded">Accent (Illuminated Gold)</div>
              <div className="p-3 bg-muted text-muted-foreground rounded">Muted</div>
            </div>
          </CardContent>
        </Card>

        {/* Typography Test */}
        <Card>
          <CardHeader>
            <CardTitle>Typography</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <h1 className="text-2xl font-bold">Heading 1</h1>
            <h2 className="text-xl font-semibold">Heading 2</h2>
            <h3 className="text-lg font-medium">Heading 3</h3>
            <p className="text-foreground">Regular paragraph text</p>
            <p className="text-muted-foreground">Muted text</p>
            <p className="text-sm text-muted-foreground">Small muted text</p>
          </CardContent>
        </Card>

        {/* Button Variants Test */}
        <Card>
          <CardHeader>
            <CardTitle>Button Variants</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full">Primary Button</Button>
            <Button variant="secondary" className="w-full">Secondary Button</Button>
            <Button variant="outline" className="w-full">Outline Button</Button>
            <Button variant="ghost" className="w-full">Ghost Button</Button>
            <Button variant="destructive" className="w-full">Destructive Button</Button>
          </CardContent>
        </Card>

        {/* Badge Test */}
        <Card>
          <CardHeader>
            <CardTitle>Badges & Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex flex-wrap gap-2">
              <Badge>Default Badge</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Success state</span>
              </div>
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span>Error state</span>
              </div>
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-blue-500" />
                <span>Info state</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Elements Test */}
        <Card>
          <CardHeader>
            <CardTitle>Form Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Input placeholder="Text input field" />
            <Input type="email" placeholder="Email input" />
            <Input type="password" placeholder="Password input" />
            <div className="flex gap-2">
              <Input placeholder="Search..." className="flex-1" />
              <Button size="icon">
                <BookOpen className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Icons Test */}
        <Card>
          <CardHeader>
            <CardTitle>Icons & Graphics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div className="space-y-2">
                <BookOpen className="h-8 w-8 mx-auto text-primary" />
                <span className="text-xs">Primary</span>
              </div>
              <div className="space-y-2">
                <Star className="h-8 w-8 mx-auto text-accent" />
                <span className="text-xs">Accent</span>
              </div>
              <div className="space-y-2">
                <Heart className="h-8 w-8 mx-auto text-red-500" />
                <span className="text-xs">Red</span>
              </div>
              <div className="space-y-2">
                <ShoppingCart className="h-8 w-8 mx-auto text-muted-foreground" />
                <span className="text-xs">Muted</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shadows & Elevation Test */}
        <Card>
          <CardHeader>
            <CardTitle>Shadows & Elevation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-card rounded shadow-soft">Soft Shadow</div>
            <div className="p-4 bg-card rounded shadow-medium">Medium Shadow</div>
            <div className="p-4 bg-card rounded shadow-large">Large Shadow</div>
          </CardContent>
        </Card>

        {/* Interactive Elements Test */}
        <Card>
          <CardHeader>
            <CardTitle>Interactive Elements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <button className="w-full p-3 bg-card hover:bg-card/80 border rounded transition-colors">
              Hover Effect Card
            </button>
            <button className="w-full p-3 bg-accent hover:bg-accent/90 text-accent-foreground rounded transition-colors">
              Accent Hover Button
            </button>
            <div className="p-3 border rounded cursor-pointer hover:border-accent transition-colors">
              Hover Border Effect
            </div>
          </CardContent>
        </Card>

        {/* Gradient Test */}
        <Card>
          <CardHeader>
            <CardTitle>Gradients & Effects</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-4 bg-gradient-to-r from-primary to-accent text-primary-foreground rounded">
              Brand Gradient
            </div>
            <div className="p-4 bg-gradient-to-br from-background to-card border rounded">
              Subtle Gradient
            </div>
            <div className="p-4 bg-gradient-to-r from-accent/20 to-primary/20 rounded">
              Accent Overlay
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Theme Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Theme Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold mb-2">Current Theme</h4>
              <p className="text-muted-foreground">
                Active theme: <span className="font-mono bg-muted px-2 py-1 rounded">{theme}</span>
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Color Values</h4>
              <div className="space-y-1 text-sm">
                <div>Background: <span className="font-mono">hsl(var(--background))</span></div>
                <div>Foreground: <span className="font-mono">hsl(var(--foreground))</span></div>
                <div>Primary: <span className="font-mono">hsl(var(--primary))</span></div>
                <div>Accent: <span className="font-mono">hsl(var(--accent))</span></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center text-muted-foreground">
        <p>✅ Enhanced theme implementation test complete</p>
        <p className="text-sm mt-2">
          All components should smoothly transition between enhanced light and dark themes
        </p>
        <p className="text-xs mt-1 opacity-75">
          Light mode now features soft off-white backgrounds for better visual comfort
        </p>
      </div>
    </div>
  );
};

export default DarkModeTest;
