
import React, { useState } from 'react';
import { X, Gift, Truck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const PromotionalBanner = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="bg-elegant-gold text-classic-black">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <Gift className="h-5 w-5" />
            <span className="font-medium">
              🎉 Grand Opening Special: Free delivery on orders over $50! 
            </span>
            <Truck className="h-4 w-4" />
          </div>
          <div className="flex items-center space-x-4">
            <Link to="/books">
              <Button size="sm" variant="outline" className="border-classic-black text-classic-black hover:bg-classic-black hover:text-elegant-gold">
                Shop Now
              </Button>
            </Link>
            <button
              onClick={() => setIsVisible(false)}
              className="hover:bg-classic-black/10 p-1 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionalBanner;
