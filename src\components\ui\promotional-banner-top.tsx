import React, { useState } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PromotionalBannerTop = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="bg-elegant-gold text-classic-black py-2 px-4 relative">
      <div className="container mx-auto flex items-center justify-center">
        <p className="text-sm font-medium text-center">
          Grand Opening Special: Free delivery in Sfax on orders over 50 TND!
        </p>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsVisible(false)}
          className="absolute right-4 h-6 w-6 text-classic-black hover:bg-classic-black/10"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default PromotionalBannerTop;
