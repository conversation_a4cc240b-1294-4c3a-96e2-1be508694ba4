import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface LoadingSkeletonProps {
  type?: 'book-card' | 'category-card' | 'text' | 'hero';
  count?: number;
  className?: string;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  type = 'book-card', 
  count = 1, 
  className = '' 
}) => {
  const renderBookCardSkeleton = () => (
    <Card className="border-0 shadow-soft overflow-hidden">
      <div className="aspect-[3/4] bg-gradient-to-br from-warm-gray-200 to-warm-gray-300 animate-shimmer bg-[length:200%_100%] bg-gradient-to-r from-warm-gray-200 via-warm-gray-100 to-warm-gray-200"></div>
      <CardContent className="p-6 space-y-4">
        {/* Rating skeleton */}
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="w-3 h-3 bg-warm-gray-200 rounded animate-shimmer"></div>
          ))}
        </div>
        
        {/* Title skeleton */}
        <div className="space-y-2">
          <div className="h-5 bg-warm-gray-200 rounded animate-shimmer"></div>
          <div className="h-5 bg-warm-gray-200 rounded w-3/4 animate-shimmer"></div>
        </div>
        
        {/* Author skeleton */}
        <div className="h-4 bg-warm-gray-200 rounded w-1/2 animate-shimmer"></div>
        
        {/* Category badge skeleton */}
        <div className="h-6 bg-warm-gray-200 rounded-full w-20 animate-shimmer"></div>
        
        {/* Price skeleton */}
        <div className="flex items-center justify-between">
          <div className="h-6 bg-warm-gray-200 rounded w-16 animate-shimmer"></div>
          <div className="h-5 bg-warm-gray-200 rounded w-12 animate-shimmer"></div>
        </div>
        
        {/* Stock status skeleton */}
        <div className="flex items-center">
          <div className="w-2 h-2 bg-warm-gray-200 rounded-full mr-2 animate-shimmer"></div>
          <div className="h-3 bg-warm-gray-200 rounded w-16 animate-shimmer"></div>
        </div>
        
        {/* Button skeleton */}
        <div className="h-10 bg-warm-gray-200 rounded animate-shimmer"></div>
      </CardContent>
    </Card>
  );

  const renderCategoryCardSkeleton = () => (
    <Card className="border-0 shadow-soft">
      <CardContent className="p-8 text-center space-y-4">
        {/* Icon skeleton */}
        <div className="w-12 h-12 bg-warm-gray-200 rounded-xl mx-auto animate-shimmer"></div>
        
        {/* Title skeleton */}
        <div className="h-5 bg-warm-gray-200 rounded animate-shimmer"></div>
        
        {/* Count skeleton */}
        <div className="h-4 bg-warm-gray-200 rounded w-16 mx-auto animate-shimmer"></div>
      </CardContent>
    </Card>
  );

  const renderTextSkeleton = () => (
    <div className={`space-y-2 ${className}`}>
      <div className="h-4 bg-warm-gray-200 rounded animate-shimmer"></div>
      <div className="h-4 bg-warm-gray-200 rounded w-3/4 animate-shimmer"></div>
      <div className="h-4 bg-warm-gray-200 rounded w-1/2 animate-shimmer"></div>
    </div>
  );

  const renderHeroSkeleton = () => (
    <div className="bg-warm-gray-100 animate-shimmer bg-[length:200%_100%] bg-gradient-to-r from-warm-gray-200 via-warm-gray-100 to-warm-gray-200">
      <div className="container mx-auto container-padding section-padding">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            {/* Badge skeleton */}
            <div className="h-8 bg-warm-gray-300 rounded-full w-48 animate-shimmer"></div>
            
            {/* Title skeleton */}
            <div className="space-y-4">
              <div className="h-12 bg-warm-gray-300 rounded animate-shimmer"></div>
              <div className="h-12 bg-warm-gray-300 rounded w-3/4 animate-shimmer"></div>
            </div>
            
            {/* Description skeleton */}
            <div className="space-y-2">
              <div className="h-6 bg-warm-gray-300 rounded animate-shimmer"></div>
              <div className="h-6 bg-warm-gray-300 rounded w-5/6 animate-shimmer"></div>
            </div>
            
            {/* Stats skeleton */}
            <div className="flex gap-8">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="text-center space-y-2">
                  <div className="h-8 bg-warm-gray-300 rounded w-12 animate-shimmer"></div>
                  <div className="h-4 bg-warm-gray-300 rounded w-16 animate-shimmer"></div>
                </div>
              ))}
            </div>
            
            {/* Buttons skeleton */}
            <div className="flex gap-4">
              <div className="h-12 bg-warm-gray-300 rounded w-32 animate-shimmer"></div>
              <div className="h-12 bg-warm-gray-300 rounded w-32 animate-shimmer"></div>
            </div>
          </div>
          
          {/* Hero image skeleton */}
          <div className="h-96 bg-warm-gray-300 rounded-2xl animate-shimmer"></div>
        </div>
      </div>
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'book-card':
        return renderBookCardSkeleton();
      case 'category-card':
        return renderCategoryCardSkeleton();
      case 'text':
        return renderTextSkeleton();
      case 'hero':
        return renderHeroSkeleton();
      default:
        return renderBookCardSkeleton();
    }
  };

  if (type === 'hero') {
    return renderSkeleton();
  }

  return (
    <>
      {[...Array(count)].map((_, index) => (
        <div key={index} className={className}>
          {renderSkeleton()}
        </div>
      ))}
    </>
  );
};

export default LoadingSkeleton;
