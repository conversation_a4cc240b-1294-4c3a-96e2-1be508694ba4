
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import BookCard from '@/components/ui/book-card';
import { useBooks } from '@/context/BookContext';
import { Link } from 'react-router-dom';
import { Clock, ArrowRight } from 'lucide-react';

const NewArrivals = () => {
  const { books } = useBooks();
  // Get the latest 3 books (simulating by taking the last 3)
  const newBooks = books.slice(-3);

  return (
    <section className="py-16 bg-gray-50 dark:bg-muted/30 transition-colors duration-300">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center space-x-3">
            <Clock className="h-8 w-8 text-elegant-gold dark:text-accent" />
            <h2 className="text-3xl font-bold text-classic-black dark:text-foreground">New Arrivals</h2>
          </div>
          <Link to="/books?sort=newest">
            <Button variant="outline" className="flex items-center space-x-2 border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground">
              <span>View All New Books</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {newBooks.map(book => (
            <div key={book.id} className="relative">
              <div className="absolute top-2 left-2 z-10">
                <span className="bg-elegant-gold dark:bg-accent text-classic-black dark:text-accent-foreground px-2 py-1 rounded-full text-xs font-bold shadow-soft dark:shadow-large">
                  NEW
                </span>
              </div>
              <BookCard book={book} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NewArrivals;
