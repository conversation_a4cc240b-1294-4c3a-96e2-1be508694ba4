// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fdcxwadiifrsibireins.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZkY3h3YWRpaWZyc2liaXJlaW5zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1OTE5OTMsImV4cCI6MjA2NjE2Nzk5M30.1kQUoAu8MHTCL5WmGTlYG_2-_Mj2gl4-_JVJbmAHB3w";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);