
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Phone, MessageCircle, MapPin, BookOpen, Mail, Facebook, Instagram, Twitter, Heart, Shield, Truck, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const Footer = () => {
  return (
    <footer className="gradient-primary text-primary-foreground mt-auto relative overflow-hidden border-t border-primary-foreground/10">
      {/* Modern Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-accent/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-primary/30 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent/10 rounded-full blur-2xl"></div>
      </div>

      <div className="relative container mx-auto container-padding py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link to="/" className="flex items-center space-x-4 mb-8 group">
              <div className="bg-accent/20 p-3 rounded-2xl group-hover:scale-110 transition-all duration-300 glow-accent">
                <BookOpen className="h-8 w-8 text-accent" />
              </div>
              <div>
                <span className="text-3xl font-bold tracking-tight">Kotobcom</span>
                <p className="text-sm text-primary-foreground/80">Your Trusted Bookstore</p>
              </div>
            </Link>
            <p className="text-primary-foreground/90 mb-8 leading-relaxed text-lg">
              Discover amazing books with convenient delivery and secure payment on arrival. Your literary journey starts here.
            </p>

            {/* Modern Social Media Links */}
            <div className="flex space-x-4">
              <Button
                size="icon"
                variant="ghost"
                className="bg-primary-foreground/10 text-primary-foreground hover:text-accent hover:bg-accent/20 transition-all duration-300 hover:scale-110 rounded-xl"
                aria-label="Follow us on Facebook"
              >
                <Facebook className="h-5 w-5" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="bg-primary-foreground/10 text-primary-foreground hover:text-accent hover:bg-accent/20 transition-all duration-300 hover:scale-110 rounded-xl"
                aria-label="Follow us on Instagram"
              >
                <Instagram className="h-5 w-5" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                className="bg-primary-foreground/10 text-primary-foreground hover:text-accent hover:bg-accent/20 transition-all duration-300 hover:scale-110 rounded-xl"
                aria-label="Follow us on Twitter"
              >
                <Twitter className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-elegant-gold">Quick Links</h4>
            <nav className="space-y-3">
              <Link to="/" className="block text-clean-white/80 hover:text-elegant-gold transition-colors duration-300 hover:translate-x-1 transform">
                Home
              </Link>
              <Link to="/books" className="block text-clean-white/80 hover:text-elegant-gold transition-colors duration-300 hover:translate-x-1 transform">
                All Books
              </Link>
              <Link to="/books?featured=true" className="block text-clean-white/80 hover:text-elegant-gold transition-colors duration-300 hover:translate-x-1 transform">
                Featured Books
              </Link>
              <Link to="/cart" className="block text-clean-white/80 hover:text-elegant-gold transition-colors duration-300 hover:translate-x-1 transform">
                Shopping Cart
              </Link>
            </nav>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-elegant-gold">Contact Us</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-elegant-gold mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-clean-white font-medium">+216 29 381 882</p>
                  <p className="text-clean-white/70 text-sm">Call us anytime</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MessageCircle className="h-5 w-5 text-elegant-gold mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-clean-white font-medium">WhatsApp Orders</p>
                  <p className="text-clean-white/70 text-sm">Quick & easy ordering</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-elegant-gold mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-clean-white font-medium">Tunisia</p>
                  <p className="text-clean-white/70 text-sm">Nationwide delivery</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-elegant-gold mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-clean-white font-medium"><EMAIL></p>
                  <p className="text-clean-white/70 text-sm">Customer support</p>
                </div>
              </div>
            </div>
          </div>

          {/* Service Features */}
          <div>
            <h4 className="text-lg font-bold mb-6 text-elegant-gold">Why Choose Us</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="bg-elegant-gold/20 p-2 rounded-lg">
                  <Shield className="h-4 w-4 text-elegant-gold" />
                </div>
                <div>
                  <p className="text-clean-white font-medium text-sm">Secure Payment</p>
                  <p className="text-clean-white/70 text-xs">Pay on delivery</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="bg-elegant-gold/20 p-2 rounded-lg">
                  <Truck className="h-4 w-4 text-elegant-gold" />
                </div>
                <div>
                  <p className="text-clean-white font-medium text-sm">Fast Delivery</p>
                  <p className="text-clean-white/70 text-xs">24-48 hours</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="bg-elegant-gold/20 p-2 rounded-lg">
                  <Heart className="h-4 w-4 text-elegant-gold" />
                </div>
                <div>
                  <p className="text-clean-white font-medium text-sm">Quality Books</p>
                  <p className="text-clean-white/70 text-xs">Carefully selected</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="bg-elegant-gold/20 p-2 rounded-lg">
                  <Clock className="h-4 w-4 text-elegant-gold" />
                </div>
                <div>
                  <p className="text-clean-white font-medium text-sm">24/7 Support</p>
                  <p className="text-clean-white/70 text-xs">Always here to help</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Special Offers Banner */}
        <div className="bg-elegant-gold/10 border border-elegant-gold/20 rounded-2xl p-6 mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-center md:text-left">
              <h5 className="text-lg font-bold text-elegant-gold mb-2">Special Offer!</h5>
              <p className="text-clean-white/90">Free delivery on orders over $50. Limited time offer.</p>
            </div>
            <Badge className="bg-elegant-gold text-classic-black px-6 py-2 text-sm font-bold">
              Save on Shipping
            </Badge>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-clean-white/20 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-center md:text-left">
              <p className="text-clean-white/80 text-sm">
                © 2024 Kotobcom. All rights reserved.
              </p>
              <p className="text-clean-white/60 text-xs mt-1">
                Made with <Heart className="h-3 w-3 inline text-red-400" /> for book lovers in Tunisia
              </p>
            </div>

            <div className="flex flex-wrap items-center gap-4 text-xs text-clean-white/60">
              <Link to="/privacy" className="hover:text-elegant-gold transition-colors">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link to="/terms" className="hover:text-elegant-gold transition-colors">
                Terms of Service
              </Link>
              <span>•</span>
              <Link to="/returns" className="hover:text-elegant-gold transition-colors">
                Return Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
