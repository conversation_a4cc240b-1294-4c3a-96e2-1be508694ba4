
import React from 'react';
import { Phone, MessageCircle, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-deep-red text-clean-white mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">Kotobcom</h3>
            <p className="text-clean-white/90">
              Your trusted bookstore for quality books with convenient delivery service.
            </p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Information</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>+216 29 381 882</span>
              </div>
              <div className="flex items-center space-x-2">
                <MessageCircle className="h-4 w-4" />
                <span>WhatsApp Orders</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Tunisia</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <div className="space-y-2">
              <div><a href="/" className="hover:text-elegant-gold transition-colors">Home</a></div>
              <div><a href="/books" className="hover:text-elegant-gold transition-colors">All Books</a></div>
              <div><a href="/cart" className="hover:text-elegant-gold transition-colors">Shopping Cart</a></div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-clean-white/20 mt-8 pt-8 text-center">
          <p className="text-clean-white/80">
            © 2024 Kotobcom. All rights reserved. | Payment on Delivery Only
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
