import React, { useState, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import BookCard from '@/components/ui/book-card';
import { useBooks } from '@/context/BookContext';

const AllBooks = () => {
  const { books, getAllCategories, loading } = useBooks();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedAuthor, setSelectedAuthor] = useState('all');
  const [sortBy, setSortBy] = useState('title');
  const [currentPage, setCurrentPage] = useState(1);
  const booksPerPage = 12;

  const categories = getAllCategories();
  const authors = [...new Set(books.map(book => book.author))];

  // Get category from URL params
  React.useEffect(() => {
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    if (category) {
      setSelectedCategory(category);
    }
    if (search) {
      setSearchTerm(search);
    }
  }, [searchParams]);

  const filteredAndSortedBooks = useMemo(() => {
    let filtered = books.filter(book => {
      const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          book.author.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
      const matchesAuthor = selectedAuthor === 'all' || book.author === selectedAuthor;
      
      return matchesSearch && matchesCategory && matchesAuthor;
    });

    // Sort books
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'author':
          return a.author.localeCompare(b.author);
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        default:
          return 0;
      }
    });

    return filtered;
  }, [books, searchTerm, selectedCategory, selectedAuthor, sortBy]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedBooks.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const currentBooks = filteredAndSortedBooks.slice(startIndex, startIndex + booksPerPage);

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setSelectedAuthor('all');
    setSortBy('title');
    setCurrentPage(1);
    setSearchParams({});
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-lg">Loading books...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="mb-12 text-center">
          <div className="badge-modern mb-6">Book Collection</div>
          <h1 className="text-5xl md:text-6xl font-bold text-foreground mb-4">All Books</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">Discover our complete collection of carefully curated books from around the world</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-12">
          {/* Modern Sidebar Filters */}
          <aside className="lg:w-80 space-y-8">
            <Card className="card-elegant p-8">
              <h3 className="font-bold text-card-foreground mb-6 flex items-center text-xl">
                <Filter className="h-6 w-6 mr-3 text-accent" />
                Filters
              </h3>
            
              {/* Modern Search */}
              <div className="mb-8">
                <label className="block text-lg font-semibold text-card-foreground mb-4">Search</label>
                <div className="relative">
                  <Search className="absolute left-4 top-4 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search books, authors, genres..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-modern pl-12 h-14 text-lg"
                  />
                </div>
              </div>

            {/* Category Filter */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-classic-black dark:text-card-foreground mb-2">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="bg-background dark:bg-input border-border dark:border-border text-foreground dark:text-foreground">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-popover border dark:border-border z-50">
                  <SelectItem value="all" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category} className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Author Filter */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-classic-black dark:text-card-foreground mb-2">Author</label>
              <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                <SelectTrigger className="bg-background dark:bg-input border-border dark:border-border text-foreground dark:text-foreground">
                  <SelectValue placeholder="All Authors" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-popover border dark:border-border z-50">
                  <SelectItem value="all" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">All Authors</SelectItem>
                  {authors.map(author => (
                    <SelectItem key={author} value={author} className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">{author}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-classic-black dark:text-card-foreground mb-2">Sort By</label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="bg-background dark:bg-input border-border dark:border-border text-foreground dark:text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-popover border dark:border-border z-50">
                  <SelectItem value="title" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">Title (A-Z)</SelectItem>
                  <SelectItem value="author" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">Author (A-Z)</SelectItem>
                  <SelectItem value="price-low" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">Price (Low to High)</SelectItem>
                  <SelectItem value="price-high" className="dark:text-popover-foreground dark:hover:bg-accent dark:hover:text-accent-foreground">Price (High to Low)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={clearFilters} variant="outline" className="w-full border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground">
              Clear Filters
            </Button>
            </Card>
          </aside>

        {/* Main Content */}
        <main className="flex-1">
          <div className="mb-6 flex justify-between items-center">
            <p className="text-gray-600 dark:text-muted-foreground">
              Showing {startIndex + 1}-{Math.min(startIndex + booksPerPage, filteredAndSortedBooks.length)} of {filteredAndSortedBooks.length} books
            </p>
          </div>

          {/* Books Grid */}
          {currentBooks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {currentBooks.map(book => (
                <BookCard key={book.id} book={book} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-card dark:bg-card rounded-lg border dark:border-border">
              <p className="text-gray-500 dark:text-muted-foreground text-lg mb-4">No books found matching your criteria.</p>
              <Button onClick={clearFilters} className="mt-4 bg-elegant-gold dark:bg-accent hover:bg-elegant-gold/90 dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground">
                Clear Filters
              </Button>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center space-x-2">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                variant="outline"
                className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground"
              >
                Previous
              </Button>

              {[...Array(totalPages)].map((_, i) => (
                <Button
                  key={i + 1}
                  onClick={() => setCurrentPage(i + 1)}
                  variant={currentPage === i + 1 ? "default" : "outline"}
                  className={currentPage === i + 1
                    ? "bg-elegant-gold dark:bg-accent hover:bg-elegant-gold/90 dark:hover:bg-accent/90 text-classic-black dark:text-accent-foreground"
                    : "border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground"
                  }
                >
                  {i + 1}
                </Button>
              ))}

              <Button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                variant="outline"
                className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-accent dark:hover:bg-accent hover:text-accent-foreground dark:hover:text-accent-foreground"
              >
                Next
              </Button>
            </div>
          )}
        </main>
        </div>
      </div>
    </div>
  );
};

export default AllBooks;
